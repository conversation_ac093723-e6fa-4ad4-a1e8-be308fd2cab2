<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MUVEAZY - UK Relocation Experts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @font-face {
            font-family: 'FuturaPTMedium';
            src: local('FuturaPTMedium'),
                url('../fonts/FuturaCyrillicMedium.ttf') format('truetype');
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #1a1a1a;
            background-color: #ffffff;
            font-family: 'FuturaPTMedium';
        }

        .hero-bg {
            position: relative;
            overflow: hidden;
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.6)),
                url('./images/panoramic-view-big-ben-london-sunset-uk.webp') no-repeat left center;
            background-size: cover;
            color: white;
            z-index: 0;
        }

        .hero-bg::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, #070511 8.47%, rgba(7, 5, 17, 0.00) 87.77%);
            z-index: 1;
            pointer-events: none;
        }


        .service-icon-container div {
            background: white !important;
        }

        .sticky-nav {
            width: 88% !important;
            left: 6%;
            top: 2%;
            border-radius: 18px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        }

        .transparent-nav {
            background: transparent;
            border: 0 !important;
            box-shadow: none;
        }

        .transparent-nav .nav-link {
            color: white;
        }

        .white-nav {
            background: #f9fafb;
            border: 2px solid white !important;
        }

        .white-nav .nav-link {
            color: #4b5563;
        }

        .uk-flag {

            width: 50px;
            height: 50px;
        }

        @keyframes floatFlag {
            0% {
                transform: translateY(-2px) rotate(0deg);
            }

            50% {
                transform: translateY(-8px) rotate(5deg);
            }

            100% {
                transform: translateY(-2px) rotate(0deg);
            }
        }

        .floating-icon {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0px) rotateY(0deg);
            }

            50% {
                transform: translateY(-15px) rotateY(15deg);
            }

            100% {
                transform: translateY(0px) rotateY(0deg);
            }
        }

        .service-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            height: 350px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        }

        .service-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.5));
            z-index: 1;
        }

        .hero-heading span {
            letter-spacing: 1px;
        }

        .service-card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .service-title-bg {
            backdrop-filter: blur(5px);
            background-color: rgb(255 255 255 / 35%);
            border-radius: 12px;
            transition: all 0.3s ease;
            text-align: center;
        }

        .service-title-bg h3 {
            color: white;
        }

        .neighborhood-section {
            position: relative;
            padding: 80px 0;
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)),
                url('./images/6823.webp');
            background-size: cover;
            background-position: center;
            color: white;
            margin: 0 40px;
            border-radius: 20px;
        }

        .neighborhood-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 100px;
        }

        .neighborhood-image {
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .neighborhood-image:hover {
            transform: scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        .testimonial-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        @media (max-width: 1024px) {
            .testimonial-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .neighborhood-container {
                padding: 0 50px;
            }
        }

        @media (max-width: 768px) {
            .testimonial-grid {
                grid-template-columns: 1fr;
            }

            .neighborhood-container {
                padding: 0 30px;
            }

            .hero-heading {
                font-size: 2.5rem;
            }

            .service-card {
                height: 280px;
            }

            .neighborhood-section {
                margin: 0 20px;
            }
        }

        @media (max-width: 640px) {
            .neighborhood-container {
                padding: 0 20px;
            }
        }

        .btn-theme {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-theme:hover {
            transform: translateY(-3px);
        }

        .logo-m {
            display: inline-block;
            width: 40px;
            height: 40px;
            background-image: url('https://cdn-icons-png.flaticon.com/512/16895/16895102.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            margin-right: 8px;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        .partner-badge {
            background: white;
            border-radius: 50px;
            padding: 8px 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .partner-badge:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }

        .city-card {
            height: 420px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .city-card:hover {
            transform: translateY(-10px);
        }

        .city-card img {
            height: 200px;
            object-fit: cover;
            width: 100%;
        }

        .city-card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 1.5rem;
        }

        /* Modern Navbar Styles */
        .nav-link {
            position: relative;
            padding: 8px 0;
            font-weight: 500;
            color: #fff;
            transition: all 0.3s ease;
        }

        .nav-link:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: #4f46e5;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: #111827;
        }

        .nav-link:hover:after {
            width: 100%;
        }

        .nav-link.active {
            color: #111827;
            font-weight: 600;
        }

        .nav-link.active:after {
            width: 100%;
        }

        .login-btn {
            align-items: center;
            background: linear-gradient(45deg, #4f46e5, #8b5cf6);
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
        }

        .mobile-menu-btn {
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            transform: scale(1.1);
        }

        .hero-gradient-text {
            background: linear-gradient(90deg, #4E81FF 0%, #7764FF 50%, #A147FF 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        @keyframes gradient {
            0% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0% 50%;
            }
        }

        .hidden-services,
        .hidden-cities {
            display: none;
        }

        .show-more {
            display: block !important;
        }

        .black-btn {
            background-color: #000;
            color: white;
            transition: all 0.3s ease;
        }

        .black-btn:hover {
            background: linear-gradient(90deg, #4E81FF 0%, #7764FF 50%, #A147FF 100%);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .nav-center {
            display: flex;
            justify-content: center;
            width: 100%;
        }
    </style>
</head>

<body>
    <nav class="sticky-nav py-4 px-6 fade-in fixed z-50">
        <div class="container mx-auto flex flex-col md:flex-row justify-between items-center">
            <div class="flex items-center space-x-2 mb-4 md:mb-0">
                <span class="logo-m"></span>
                <span
                    class="text-xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">MUVEAZY</span>
            </div>
            <div class="nav-center hidden md:flex space-x-8 items-center">
                <a href="#services" class="nav-link">Services</a>
                <a href="#community" class="nav-link">Community Advice</a>
                <a href="#support" class="nav-link">Support</a>
                <a href="#jobs" class="nav-link">Jobs in UK</a>
                <a href="#legal" class="nav-link">Legal Help</a>
            </div>
            <button class="login-btn px-6 flex py-2 rounded-full font-medium text-white shadow-md">
                Login <i class="fas fa-arrow-right ml-2 text-sm"></i>
            </button>
            <button class="md:hidden mobile-menu-btn">
                <i class="fas fa-bars text-xl text-gray-800"></i>
            </button>
        </div>
    </nav>

    <section class="hero-bg min-h-screen flex items-center justify-center text-center px-4 pt-20">
        <div class="hero-content container mx-auto max-w-5xl relative z-10">
            <h1 class="hero-heading text-5xl md:text-6xl font-bold mb-6 leading-tight text-white">
                <span class="block hero-gradient-text ">Make the
                    <img src="./images/uk-flag.png" alt="UK Flag" class="uk-flag mb-3 inline ">
                    Your New Home
                </span>
                <span class="block">With Confidence & Support</span>
            </h1>
            <p class="text-lg md:text-xl mb-10 max-w-3xl mx-auto text-gray-200 fade-in-delay-2">
                MUVEAZY is your one-stop UK relocation platform covering housing, education, finance, and local
                integration. We provide personalized guidance to make your transition smooth and successful.
            </p>
            <button class="black-btn px-12 py-4 rounded-full font-medium text-lg text-white">
                <span>Get Started <i class="fas fa-arrow-right ml-2"></i></span>
            </button>

            <div class="mt-20 flex flex-wrap justify-center gap-6 md:gap-10">
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #FF6B6B;">
                        <i class="fas fa-home text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Housing</span>
                </div>

                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #4ADE80;">
                        <i class="fas fa-graduation-cap text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Education</span>
                </div>

                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 px-7 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #FACC15;">
                        <i class="fas fa-pound-sign text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Finance</span>
                </div>

                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #C084FC;">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Community</span>
                </div>

                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 px-6 transition duration-300"
                        style="color: #FBBF24;">
                        <i class="fas fa-briefcase text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Jobs</span>
                </div>

                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #38BDF8;">
                        <i class="fas fa-balance-scale text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Legal</span>
                </div>
            </div>

        </div>
    </section>

    <section id="services" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2
                    class="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-purple-500 to-blue-500 bg-clip-text text-transparent">
                    Our Core Services</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Comprehensive relocation services tailored to your needs</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="service-card rounded-xl"
                    style="background-image: url('./images/zebra-crossing-front-residential-houses.webp'); background-size: cover; background-repeat: no-repeat; background-position: center;">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Housing</h3>
                        </div>
                    </div>
                </div>


                <div class="service-card rounded-xl"
                    style="background-image: url('./images/english-book-resting-table-working-space.webp'); background-size: cover; background-repeat: no-repeat; background-position: center;">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Schools</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('./images/businessman-woman-workplace-paperwork-dollar.webp'); background-size: cover; background-repeat: no-repeat; background-position: center;">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Banking</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('./images/people-having-debate-while-looking-computer.webp'); background-size: cover; background-repeat: no-repeat; background-position: center;">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Community Advice</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hidden Services -->
            <div class="hidden-services grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mt-8">
                <div class="service-card rounded-xl"
                    style="background-image: url('./images/doctor-shook-hands-encourage-patient-after-informing-results-examination-informing-patients-about-treatment-guidelines-prescribing-medicines-disease-examination-concept.webp'); background-size: cover; background-repeat: no-repeat; background-position: center;">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Healthcare</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('./images/lawyer-office-employee-civil-servant-near-his-workplace-with-great-britain-flag-jacket-icon.webp'); background-size: cover; background-repeat: no-repeat; background-position: center;">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Taxation</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('./images/delivery-person-getting-parcel-out-delivery.webp'); background-size: cover; background-repeat: no-repeat; background-position: center;">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Removals</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('./images/medium-shot-woman-working-office-travel-agency.webp'); background-size: cover; background-repeat: no-repeat; background-position: center;">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Mobile Services</h3>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="btn-theme px-10 py-3 rounded-full font-medium text-lg bg-black text-white">
                    View All Services <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <section class="py-16 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12 text-black">Our Trusted Partners</h2>

            <div class="flex flex-wrap justify-center items-center gap-4 md:gap-6">
                <div class="partner-badge">
                    <span class="font-medium">HSBC</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Barclays</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Rightmove</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">GOV.UK</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">British Council</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">NHS</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Uber</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Vodafone</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Lloyds</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Santander</span>
                </div>
            </div>
        </div>
    </section>

    <section id="cities" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2
                    class="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-purple-500 to-blue-500 bg-clip-text text-transparent">
                    Explore UK Cities</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Discover the perfect city for your new life in the UK</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="London" class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">London</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££££ Expensive</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">The vibrant capital with endless opportunities and diverse
                            communities.</p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,800</span>
                            <span><i class="fas fa-users mr-1"></i> 8.9M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./images/cityscape-frankfurt-covered-modern-buildings-sunset-germany.webp"
                        alt="Manchester" class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Manchester</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">A thriving northern city with great culture and affordable living.
                        </p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £950</span>
                            <span><i class="fas fa-users mr-1"></i> 2.8M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./images/edinburgh-city-view-panorama-night-uk.webp" alt="Edinburgh"
                        class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Edinburgh</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's beautiful capital with historic charm and great
                            universities.</p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,100</span>
                            <span><i class="fas fa-users mr-1"></i> 548,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./images/day-city-view.webp" alt="Birmingham" class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Birmingham</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">England's second city with a thriving business scene and cultural
                            diversity.</p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £850</span>
                            <span><i class="fas fa-users mr-1"></i> 1.1M people</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hidden Cities -->
            <div class="hidden-cities grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mt-8">
                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./images/beautiful-manhattan-bridge-new-york-usa.webp" alt="Liverpool"
                        class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Liverpool</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Famous for its music scene, friendly locals, and maritime history.
                        </p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £750</span>
                            <span><i class="fas fa-users mr-1"></i> 498,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./images/aerial-view-shot-rotterdam-city-netherlands.webp" alt="Glasgow"
                        class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Glasgow</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's largest city with vibrant arts scene and friendly
                            atmosphere.</p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £800</span>
                            <span><i class="fas fa-users mr-1"></i> 635,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./images/wide-angle-shot-city-bristol-uk.webp" alt="Bristol"
                        class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Bristol</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Creative city with strong tech sector and excellent quality of
                            life.</p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,050</span>
                            <span><i class="fas fa-users mr-1"></i> 467,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="./images/dunster-house-cambridge-usa.webp" alt="Leeds" class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Leeds</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Fast-growing northern city with excellent job opportunities.</p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £850</span>
                            <span><i class="fas fa-users mr-1"></i> 793,000 people</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button id="viewCitiesBtn"
                    class="btn-theme px-10 py-3 rounded-full font-medium text-lg bg-gradient-to-r from-yellow-400 to-purple-500 text-white">
                    Explore More Cities <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">UK Survival Toolkit</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Essential knowledge and resources to help you settle in the UK quickly and comfortably.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-university"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Open a Bank Account</h3>
                    <p class="text-gray-600 text-sm">
                        Step-by-step guide to opening your first UK bank account as a newcomer.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Get NHS Number</h3>
                    <p class="text-gray-600 text-sm">
                        How to register with a GP and access UK healthcare services.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Essential Documents</h3>
                    <p class="text-gray-600 text-sm">
                        Checklist of documents needed for housing, schools, and employment.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-train"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Public Transport</h3>
                    <p class="text-gray-600 text-sm">
                        Navigating buses, trains, and the London Underground system.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="btn-theme px-10 py-3 rounded-full font-medium text-lg bg-black text-white">
                    <span>Download Full Toolkit (PDF)</span>
                </button>
            </div>
        </div>
    </section>

    <section class="neighborhood-section">
        <div class="neighborhood-container text-center">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 text-white">Find Your Perfect Neighborhood in the UK</h2>
                <p class="text-xl md:text-2xl mb-8 text-white">
                    Discover the best areas to live based on your lifestyle, budget, and family needs. Our
                    comprehensive guides help you make informed decisions about where to settle.
                </p>
                <div class="flex justify-center">
                    <button
                        class="bg-white text-black px-12 py-4 rounded-full font-medium text-lg hover:bg-gray-100 shadow-lg">
                        Explore Neighborhoods <i class="fas fa-search ml-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">What Our Customers Say</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Hear from families who have successfully relocated to the UK with our help.
            </p>

            <div class="testimonial-grid mb-12">
                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/43.webp" alt="Sophie Martin"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Sophie Martin</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/fr.png" alt="France" class="w-5 mr-2">
                                <span class="text-gray-600">From France • March 2023</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "MUVEAZY made our move to London seamless. Their housing specialist found us the perfect
                        apartment in just two weeks. The schooling advice was invaluable for our children's transition."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/32.webp" alt="Raj Patel"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Raj Patel</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 mr-2">
                                <span class="text-gray-600">From India • January 2023</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "As a single professional moving to Manchester, I didn't know where to start. MUVEAZY's
                        relocation plan covered everything from visa support to finding the right neighborhood for young
                        professionals."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/65.webp" alt="Elena Rodriguez"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Elena Rodriguez</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/es.png" alt="Spain" class="w-5 mr-2">
                                <span class="text-gray-600">From Spain • November 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The community adviser connected us with other Spanish families in Edinburgh before we even
                        arrived. Having that support network made all the difference in settling in."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                    </div>
                </div>
            </div>

            <div class="testimonial-grid">
                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/45.webp" alt="Michael Johnson"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Michael Johnson</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/us.png" alt="USA" class="w-5 mr-2">
                                <span class="text-gray-600">From USA • September 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The legal support team helped navigate my work visa requirements with ease. They anticipated
                        every question I had before I even asked."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/33.webp" alt="Aisha Mohammed"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Aisha Mohammed</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/ae.png" alt="UAE" class="w-5 mr-2">
                                <span class="text-gray-600">From UAE • July 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "Finding halal food options and mosques in Birmingham was my biggest concern. MUVEAZY's cultural
                        integration guide was exactly what I needed."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="far fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/55.webp" alt="Wei Zhang"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Wei Zhang</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/cn.png" alt="China" class="w-5 mr-2">
                                <span class="text-gray-600">From China • May 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The Mandarin-speaking adviser helped my elderly parents feel comfortable with the healthcare
                        system. This personal touch meant everything."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-black text-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Begin Your UK Journey?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Get personalized relocation support tailored to your specific needs and circumstances.
            </p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <button class="bg-white text-black px-12 py-4 rounded-full font-medium hover:bg-gray-100 shadow-lg">
                    <span>Get Started</span>
                </button>
                <button
                    class="border-2 border-white text-white px-12 py-4 rounded-full font-medium hover:bg-white hover:text-black text-lg transition duration-300">
                    Speak to an Adviser
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-white py-12 px-6 border-t border-gray-200">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="logo-m"></span>
                        <span
                            class="text-xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">MUVEAZY</span>
                    </div>
                    <p class="text-gray-600 mb-4">
                        Helping individuals and families relocate to the UK with confidence since 2015.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#services" class="text-gray-600 hover:text-black">Housing Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Schooling Guidance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Visa Support</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Job Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Legal Support</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#cities" class="text-gray-600 hover:text-black">UK City Guides</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Cost of Living</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Relocation Checklist</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">UK Culture Guide</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Expat Community</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Newsletter</h3>
                    <p class="text-gray-600 mb-4">
                        Subscribe for relocation tips and UK updates.
                    </p>
                    <form class="flex">
                        <input type="email" placeholder="Your email"
                            class="px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-black w-full">
                        <button type="submit" class="bg-black text-white px-4 py-3 rounded-r-lg">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-600 mb-4 md:mb-0">
                    © 2023 MUVEAZY. All rights reserved.
                </p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-600 hover:text-black">Privacy Policy</a>
                    <a href="#" class="text-gray-600 hover:text-black">Terms of Service</a>
                    <a href="#" class="text-gray-600 hover:text-black">Contact Us</a>
                </div>
            </div>

            <div class="text-center mt-8">
                <p class="text-gray-600">
                    Made with <i class="fas fa-heart" style="color: #4441da;"></i> for UK Movers
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Define these variables at the top of your script
        const navbar = document.querySelector('.sticky-nav');
        const heroSection = document.querySelector('.hero-bg');

        // Sticky nav active link highlighting
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');

        window.addEventListener('scroll', () => {
            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;

                if (window.pageYOffset >= (sectionTop - 300)) {
                    current = section.getAttribute('id');

                    // Get the hero section's bottom position
                    const heroBottom = heroSection.offsetTop + heroSection.offsetHeight;

                    // Check if we've scrolled past the hero section
                    if (window.pageYOffset > heroBottom - 100) {
                        navbar.classList.remove('transparent-nav');
                        navbar.classList.add('white-nav');
                    } else {
                        navbar.classList.add('transparent-nav');
                        navbar.classList.remove('white-nav');
                    }
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').includes(current)) {
                    link.classList.add('active');
                }
            });
        });

        // Mobile menu toggle (would need implementation)
        const mobileMenuButton = document.querySelector('.md\\:hidden');
        if (mobileMenuButton) {
            mobileMenuButton.addEventListener('click', () => {
                // This would need a mobile menu implementation
                alert('Mobile menu would open here');
            });
        }

        // View All Services functionality
        const viewServicesBtn = document.getElementById('viewServicesBtn');
        const hiddenServices = document.querySelector('.hidden-services');

        viewServicesBtn.addEventListener('click', function () {
            hiddenServices.classList.toggle('show-more');
            if (hiddenServices.classList.contains('show-more')) {
                viewServicesBtn.innerHTML = 'Show Less <i class="fas fa-arrow-up ml-2"></i>';
            } else {
                viewServicesBtn.innerHTML = 'View All Services <i class="fas fa-arrow-down ml-2"></i>';
            }
        });

        // View All Cities functionality
        const viewCitiesBtn = document.getElementById('viewCitiesBtn');
        const hiddenCities = document.querySelector('.hidden-cities');

        viewCitiesBtn.addEventListener('click', function () {
            hiddenCities.classList.toggle('show-more');
            if (hiddenCities.classList.contains('show-more')) {
                viewCitiesBtn.innerHTML = 'Show Less <i class="fas fa-arrow-up ml-2"></i>';
            } else {
                viewCitiesBtn.innerHTML = 'Explore More Cities <i class="fas fa-arrow-down ml-2"></i>';
            }
        });

        // Add this to your existing script section
        function updateNavbar() {
            // Get the hero section's bottom position
            const heroBottom = heroSection.offsetTop + heroSection.offsetHeight;

            // Check if we've scrolled past the hero section
            if (window.pageYOffset > heroBottom - 100) {
                navbar.classList.remove('transparent-nav');
                navbar.classList.add('white-nav');
            } else {
                navbar.classList.add('transparent-nav');
                navbar.classList.remove('white-nav');
            }
        }

        // Run on scroll
        window.addEventListener('scroll', updateNavbar);

        // Initialize navbar state on page load
        window.addEventListener('DOMContentLoaded', () => {
            updateNavbar();
        });
    </script>
</body>

</html>
<!-- prompt -1 -->
<!-- I want to create a website which is help someone who is shift from their country to other country. but this webpage is special for UK, it is great at UK. i want to a page which is give country relocation guide webpage. in that first make a navbar in that make a simple icon and my site name id Globreo. and make a logo according to this, then add services, community adviser, support (i want more 2-3 according to webpage subject) and in the end add login button. now let's move to hero section, i want to make hero section like when someone see the hero section then it say directly like it is great in UK (EX. You can add the famous thing photo in the background of hero section or you can add the UK map which is automatically animate or also you can make your idea you are free to think out of the box and apply it.) in the hero section add a big superb heading of 2 lines and also add the 3-4 lines description about webpage's subject. and also add the get started button which have a superb animation (make design like when user see that then it automatically click the button).now le's move to the services, in the services i have think 4 services housing, school, banking, community advice and also add a button of view all i want minimum 10 - 12 services also in the background of the card add the image related to title. then let's move to our partners add 8 famous services which is provide services like me or add a high level companies which is based in UK and who have world wide name. then add 2 more sections i leaves on you. then next section add a beautiful heading on image of UK's bungalows or street find your perfect neighborhood in the UK and add a best 2 lines description on this and add get started button. then next 1 section i leaves on you. let's move forward to next section which is what our customers are says (testimonials) in that show 3 testimonials which is looks like real human's testimonials not looks like written by AI (In different languages). in this part make 6-7 testimonials which is automatic slides left to right and also add arrows to to slide. then our last section which is footer. you can make it as you want. and now let's talk about the webpage theme, i want white theme based webpage, and also i all design in black and white theme, and make sure that i want the modern design not a regular design. -->


<!-- Prompt -2 -->
<!-- in the hero section make heading small and in the background add An animated UK map as the background, and in headers set the section which are inside of whole page when i click on that then scroll and take me to that section, In  our core services first show only 4 services when click on view all then show other services, add some real trusted partners with their logo and name, do the same with Explore UK Cities in that also make same like Our Core Services section, in What Our Customers Say i want infinight loop of testimonials, also check for the responsive, i can't see the images in Explore UK Cities -->

<!-- prompt -3 -->
<!-- Give this all to a superb animation and you can do like in the hero section heading give the main words a animated gradient colors and can do the same with all the section main title, make the hero section heading text bigger, in core services give the proper images relates to the service, in partners section add the partner which logo you have change the explore UK cities images which is showable, and give the animations as you can give to this webpage -->


<!-- prompt-4 -->
<!-- use this url https://img.freepik.com/premium-photo/london-tower-bridge-thames-river_79295-6823.jpg?w=1380 and set this in the hero section bg, and for services section home- https://img.freepik.com/free-photo/front-view-front-door-with-white-wall-plants_23-2149360608.jpg?t=st=1747978103~exp=1747981703~hmac=da2efafbfd8b4c29c6ece6c3ab503f39361299d29cc5399ba63a89ea6ee3ad23&w=740 -->

<!-- prompt -5 -->
<!-- Create a responsive layout that features floating circular service icons positioned around a central area. The icons should visually represent core relocation services such as:  ⚠️ Alerts or safety info  🖼️ Layout & Positioning: Place 3 icons on the left side and 3 icons on the right side of the screen.  Icons should be strategically spaced vertically and offset horizontally to create a natural, asymmetrical flow.  Each icon is enclosed in a round colored marker (e.g., purple, green, blue, yellow) with a soft shadow for a floating, elevated feel.  The icons should appear to hover over a light abstract city map or road pattern background, giving a location-based context.  🎨 Style & Appearance: Each icon is simple and bold, using flat or slightly shaded colors with good contrast.  Circular markers should be approximately 50–70px in diameter, scalable for responsiveness.  Add subtle hover effects like scaling or glowing to make them interactive.  Icons can optionally include a white inner circle or border for contrast.  📐 Technical Suggestions: Use absolute positioning within a relatively positioned container to float the icons.  Ensure responsiveness by adjusting icon positions for smaller screens (e.g., stack or align diagonally).  Optionally, animate icons on page load for a smooth fade-in or float-up effect.  🔥 Goal: Create a modern, welcoming, and visually engaging interface that draws attention to key service categories in a clean, non-cluttered way. The floating icons should feel interactive and map-oriented, guiding users intuitively.   Add the background: linear-gradient(94deg, #FFBF00 4.29%, #E151FF 20.14%, #9C83FF 61.14%, #714DFF 92.82%); to the main titles. in hero section title use UK country flag instead of write UK.  in the background of hero section use this image https://img.freepik.com/free-photo/big-ben-houses-parliament-london-uk_268835-1400.jpg?t=st=1747978770~exp=1747982370~hmac=49d3e4e1b87d77b54307bfcd1357288bbacfb73f9c0cee10ca817a4710136158&w=1380  and change the button theme according to the section theme.  make header's bg blur to see the bg image.  and also change logo in header i want in this format logo MUVEAZY, change partners only show names i want total 10 partners and anyone, change design of Our Core Services section, in Explore UK Cities section i want total 4 in a row as it is in second row and then manage as the screen size, in find neighnorhood section remove the primary color and make explore neighborhoods in center
-->


<!-- prompt-6 -->
<!--  
1. use this URl https://cdn-icons-png.flaticon.com/512/197/197374.png as UK flag.
2. in explore UK cities card's make proper responsive.
3. change logo of Site here is the logo use this https://cdn-icons-png.flaticon.com/512/16895/16895102.png
4. Make navbar more attractive. and modern design.
-->