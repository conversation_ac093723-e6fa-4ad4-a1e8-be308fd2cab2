<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~prompt - 1~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<!-- <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Globreo - UK Relocation Experts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #000000;
            --secondary: #ffffff;
            --accent: #f8fafc;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: var(--primary);
            background-color: var(--secondary);
            scroll-behavior: smooth;
        }

        .hero-bg {
            background-image: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8)),
                url('https://images.unsplash.com/photo-1486299267070-83823f5448dd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        .neighborhood-bg {
            background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
                url('https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
        }

        .sticky-nav {
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
        }

        .nav-link {
            position: relative;
        }

        .nav-link:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: var(--primary);
            transition: width 0.3s ease;
        }

        .nav-link:hover:after {
            width: 100%;
        }

        .nav-link.active:after {
            width: 100%;
        }

        .btn-magnetic {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-magnetic:hover {
            transform: scale(1.05);
        }

        .btn-magnetic:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .btn-magnetic:focus:after,
        .btn-magnetic:hover:after {
            animation: ripple 1s ease-out;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }

            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }

        .service-card {
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .service-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7));
        }

        .service-card-content {
            position: relative;
            z-index: 1;
        }

        .service-title-bg {
            backdrop-filter: blur(5px);
            background-color: rgba(255, 255, 255, 0.8);
        }

        .partner-logo {
            filter: grayscale(100%);
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .partner-logo:hover {
            filter: grayscale(0%);
            opacity: 1;
            transform: scale(1.1);
        }

        .city-card {
            transition: all 0.3s ease;
        }

        .city-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .toolkit-item {
            transition: all 0.3s ease;
        }

        .toolkit-item:hover {
            transform: translateY(-3px);
            background-color: #f8fafc;
        }

        .testimonial-slide {
            transition: all 0.5s ease;
        }

        .bounce-hover {
            transition: all 0.3s ease;
        }

        .bounce-hover:hover {
            transform: translateY(-3px);
        }

        .counter {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary);
        }

        .globe-logo {
            position: relative;
            display: inline-block;
        }

        .globe-logo:before {
            content: '';
            position: absolute;
            top: -2px;
            right: -8px;
            width: 12px;
            height: 12px;
            background-color: #ef4444;
            border-radius: 50%;
            transform: rotate(15deg);
        }

        /* New styles for improved sections */
        .service-icon-container {
            transition: all 0.3s ease;
        }

        .service-icon-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .hidden-services {
            display: none;
        }

        .show-more {
            display: block;
        }

        .testimonial-container {
            position: relative;
            overflow: hidden;
        }

        .testimonial-track {
            display: flex;
            transition: transform 0.5s ease;
        }

        .testimonial-card {
            min-width: 100%;
            padding: 0 15px;
            box-sizing: border-box;
        }

        @media (min-width: 768px) {
            .testimonial-card {
                min-width: 50%;
            }
        }

        @media (min-width: 1024px) {
            .testimonial-card {
                min-width: 33.333%;
            }
        }

        .testimonial-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            z-index: 10;
        }

        .testimonial-prev {
            left: 10px;
        }

        .testimonial-next {
            right: 10px;
        }

        .testimonial-dots {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .testimonial-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ccc;
            margin: 0 5px;
            cursor: pointer;
        }

        .testimonial-dot.active {
            background: #000;
        }

        .hidden-cities {
            display: none;
        }
    </style>
</head>

<body>
    <nav class="sticky-nav py-4 px-6">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="relative">
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="18" fill="#000000" />
                        <path d="M20 2C15 2 10 5 7 9" stroke="white" stroke-width="2" />
                        <path d="M20 38C25 38 30 35 33 31" stroke="white" stroke-width="2" />
                        <path d="M2 20H38" stroke="white" stroke-width="2" />
                        <circle cx="20" cy="20" r="10" fill="#EF4444" />
                        <path d="M20 10V30" stroke="white" stroke-width="2" />
                        <path d="M10 20H30" stroke="white" stroke-width="2" />
                    </svg>
                </div>
                <span class="text-xl font-bold tracking-tight">GLOBREO</span>
            </div>
            <div class="hidden md:flex space-x-8">
                <a href="#services" class="nav-link">Services</a>
                <a href="#community" class="nav-link">Community Advice</a>
                <a href="#support" class="nav-link">Support</a>
                <a href="#jobs" class="nav-link">Jobs in UK</a>
                <a href="#legal" class="nav-link">Legal Help</a>
            </div>
            <div class="flex items-center space-x-4">
                <button
                    class="hidden md:flex items-center px-6 py-2 border border-black rounded-full hover:bg-black hover:text-white transition duration-300">
                    Login <i class="fas fa-arrow-right ml-2 text-sm"></i>
                </button>
                <button class="md:hidden">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </nav>

    <section class="hero-bg min-h-screen flex items-center justify-center text-center px-4">
        <div class="container mx-auto max-w-5xl">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                <span class="block">Make the UK Your New Home</span>
                <span class="block">With Confidence & Support</span>
            </h1>
            <p class="text-lg md:text-xl mb-10 max-w-3xl mx-auto text-gray-700">
                Globreo is your one-stop UK relocation platform covering housing, education, finance, and local
                integration.
                We provide personalized guidance to make your transition smooth and successful.
            </p>
            <button
                class="btn-magnetic bg-black text-white px-10 py-4 rounded-full font-medium text-lg hover:bg-gray-800">
                Get Started <i class="fas fa-arrow-right ml-2"></i>
            </button>

            <div class="mt-20 flex justify-center space-x-10">
                <div class="flex flex-col items-center service-icon-container">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-home text-2xl text-blue-600"></i>
                    </div>
                    <span class="text-sm font-medium">Housing</span>
                </div>
                <div class="flex flex-col items-center service-icon-container">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-graduation-cap text-2xl text-purple-600"></i>
                    </div>
                    <span class="text-sm font-medium">Education</span>
                </div>
                <div class="flex flex-col items-center service-icon-container">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-pound-sign text-2xl text-green-600"></i>
                    </div>
                    <span class="text-sm font-medium">Finance</span>
                </div>
                <div class="flex flex-col items-center service-icon-container">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-users text-2xl text-orange-600"></i>
                    </div>
                    <span class="text-sm font-medium">Community</span>
                </div>
            </div>
        </div>
    </section>

    <section id="services" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold">Our Core Services</h2>
                <button id="view-all-services" class="text-black font-medium flex items-center">
                    View All <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Housing</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1588072432836-e10032774350?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Schools</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-**********-3a14e253d987?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Banking</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-*************-49953e39b3ac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2032&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Community Advice</h3>
                        </div>
                    </div>
                </div>
            </div>

            <div id="hidden-services" class="hidden-services grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-*************-7986c2920216?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Job Assistance</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1605106702734-205df224ecce?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">UK Visa Guidance</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Healthcare Registration</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Legal Support</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-16 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12">Our Trusted Partners</h2>

            <div class="flex flex-wrap justify-center items-center gap-8 md:gap-16">
                <div class="partner-logo-container">
                    <i class="fab fa-hsbc text-5xl text-blue-800 partner-logo" title="HSBC"></i>
                </div>
                <div class="partner-logo-container">
                    <i class="fas fa-university text-5xl text-blue-600 partner-logo" title="Barclays"></i>
                </div>
                <div class="partner-logo-container">
                    <i class="fas fa-home text-5xl text-red-600 partner-logo" title="Rightmove"></i>
                </div>
                <div class="partner-logo-container">
                    <i class="fas fa-landmark text-5xl text-gray-800 partner-logo" title="GOV.UK"></i>
                </div>
                <div class="partner-logo-container">
                    <i class="fas fa-graduation-cap text-5xl text-blue-700 partner-logo" title="British Council"></i>
                </div>
                <div class="partner-logo-container">
                    <i class="fas fa-hospital text-5xl text-blue-500 partner-logo" title="NHS"></i>
                </div>
                <div class="partner-logo-container">
                    <i class="fab fa-uber text-5xl text-black partner-logo" title="Uber"></i>
                </div>
                <div class="partner-logo-container">
                    <i class="fas fa-phone-alt text-5xl text-red-600 partner-logo" title="Vodafone"></i>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">Explore UK Cities</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="London" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">London</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££££ Expensive</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">The vibrant capital with endless opportunities and diverse
                            communities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,800</span>
                            <span><i class="fas fa-users mr-1"></i> 8.9M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                        alt="Manchester" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Manchester</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">A thriving northern city with great culture and affordable living.
                        </p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £950</span>
                            <span><i class="fas fa-users mr-1"></i> 2.8M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1508050919630-b135583b29ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Edinburgh" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Edinburgh</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's beautiful capital with historic charm and great
                            universities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,100</span>
                            <span><i class="fas fa-users mr-1"></i> 548,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden-cities city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                        alt="Birmingham" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Birmingham</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">England's second city with a thriving business scene and cultural
                            diversity.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £850</span>
                            <span><i class="fas fa-users mr-1"></i> 1.1M people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden-cities city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                        alt="Glasgow" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Glasgow</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's largest city with vibrant arts scene and friendly
                            locals.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £750</span>
                            <span><i class="fas fa-users mr-1"></i> 635,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden-cities city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                        alt="Liverpool" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Liverpool</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Famous for its music heritage, waterfront and friendly locals.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £700</span>
                            <span><i class="fas fa-users mr-1"></i> 498,000 people</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button id="explore-more-cities"
                    class="bg-black text-white px-8 py-3 rounded-full font-medium hover:bg-gray-800 transition duration-300">
                    Explore More Cities
                </button>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4">UK Survival Toolkit</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Essential knowledge and resources to help you settle in the UK quickly and comfortably.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4 text-black">
                        <i class="fas fa-university"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Open a Bank Account</h3>
                    <p class="text-gray-600 text-sm">
                        Step-by-step guide to opening your first UK bank account as a newcomer.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4 text-black">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Get NHS Number</h3>
                    <p class="text-gray-600 text-sm">
                        How to register with a GP and access UK healthcare services.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4 text-black">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Essential Documents</h3>
                    <p class="text-gray-600 text-sm">
                        Checklist of documents needed for housing, schools, and employment.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4 text-black">
                        <i class="fas fa-train"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Public Transport</h3>
                    <p class="text-gray-600 text-sm">
                        Navigating buses, trains, and the London Underground system.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <button
                    class="bg-black text-white px-8 py-3 rounded-full font-medium hover:bg-gray-800 transition duration-300">
                    Download Full Toolkit (PDF)
                </button>
            </div>
        </div>
    </section>

    <section class="neighborhood-bg py-32 px-6 text-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Find Your Perfect Neighborhood in the UK</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Discover the best areas to live based on your lifestyle, budget, and family needs.
            </p>
            <button
                class="bounce-hover bg-white text-black px-10 py-4 rounded-full font-medium text-lg hover:bg-gray-100 shadow-lg">
                Get Started <i class="fas fa-search ml-2"></i>
            </button>
        </div>
    </section>

    <section class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4">What Our Customers Say</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Hear from families who have successfully relocated to the UK with our help.
            </p>

            <div class="testimonial-container relative max-w-6xl mx-auto">
                <div class="testimonial-track flex" id="testimonial-track">
                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="Sophie Martin"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Sophie Martin</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/fr.png" alt="France" class="w-5 mr-2">
                                        <span class="text-gray-600">From France • March 2023</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "Globreo made our move to London seamless. Their housing specialist found us the perfect
                                apartment in just two weeks. The schooling advice was invaluable for our children's
                                transition."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Raj Patel"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Raj Patel</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 mr-2">
                                        <span class="text-gray-600">From India • January 2023</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "As a single professional moving to Manchester, I didn't know where to start. Globreo's
                                relocation plan covered everything from visa support to finding the right neighborhood
                                for young professionals."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Elena Rodriguez"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Elena Rodriguez</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/es.png" alt="Spain" class="w-5 mr-2">
                                        <span class="text-gray-600">From Spain • November 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "The community adviser connected us with other Spanish families in Edinburgh before we
                                even arrived. Having that support network made all the difference in settling in."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star-half-alt text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Michael Johnson"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Michael Johnson</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/us.png" alt="USA" class="w-5 mr-2">
                                        <span class="text-gray-600">From USA • September 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "The legal support team helped navigate my work visa requirements with ease. They
                                anticipated every question I had before I even asked."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Aisha Mohammed"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Aisha Mohammed</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/ae.png" alt="UAE" class="w-5 mr-2">
                                        <span class="text-gray-600">From UAE • July 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "Finding halal food options and mosques in Birmingham was my biggest concern. Globreo's
                                cultural integration guide was exactly what I needed."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="far fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/men/55.jpg" alt="Wei Zhang"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Wei Zhang</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/cn.png" alt="China" class="w-5 mr-2">
                                        <span class="text-gray-600">From China • May 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "The Mandarin-speaking adviser helped my elderly parents feel comfortable with the
                                healthcare system. This personal touch meant everything."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="testimonial-nav testimonial-prev">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="testimonial-nav testimonial-next">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="testimonial-dots flex justify-center mt-8" id="testimonial-dots">
                <button class="testimonial-dot active" data-index="0"></button>
                <button class="testimonial-dot" data-index="1"></button>
                <button class="testimonial-dot" data-index="2"></button>
                <button class="testimonial-dot" data-index="3"></button>
                <button class="testimonial-dot" data-index="4"></button>
                <button class="testimonial-dot" data-index="5"></button>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-black text-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Begin Your UK Journey?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Get personalized relocation support tailored to your specific needs and circumstances.
            </p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <button
                    class="btn-magnetic bg-white text-black px-10 py-4 rounded-full font-medium hover:bg-gray-200 text-lg">
                    Get Started
                </button>
                <button
                    class="btn-magnetic border border-white text-white px-10 py-4 rounded-full font-medium hover:bg-gray-900 text-lg">
                    Speak to an Adviser
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-white py-12 px-6 border-t border-gray-200">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="relative">
                            <svg width="40" height="40" viewBox="0 0 40 40" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <circle cx="20" cy="20" r="18" fill="#000000" />
                                <path d="M20 2C15 2 10 5 7 9" stroke="white" stroke-width="2" />
                                <path d="M20 38C25 38 30 35 33 31" stroke="white" stroke-width="2" />
                                <path d="M2 20H38" stroke="white" stroke-width="2" />
                                <circle cx="20" cy="20" r="10" fill="#EF4444" />
                                <path d="M20 10V30" stroke="white" stroke-width="2" />
                                <path d="M10 20H30" stroke="white" stroke-width="2" />
                            </svg>
                        </div>
                        <span class="text-xl font-bold tracking-tight">GLOBREO</span>
                    </div>
                    <p class="text-gray-600 mb-4">
                        Helping individuals and families relocate to the UK with confidence since 2015.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 hover:text-black">Housing Assistance</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Schooling Guidance</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Visa Support</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Job Assistance</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Legal Support</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 hover:text-black">UK City Guides</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Cost of Living</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Relocation Checklist</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">UK Culture Guide</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Expat Community</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Newsletter</h3>
                    <p class="text-gray-600 mb-4">
                        Subscribe for relocation tips and UK updates.
                    </p>
                    <form class="flex">
                        <input type="email" placeholder="Your email"
                            class="px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-black w-full">
                        <button type="submit" class="bg-black text-white px-4 py-3 rounded-r-lg hover:bg-gray-800">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-600 mb-4 md:mb-0">
                    © 2023 Globreo. All rights reserved.
                </p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-600 hover:text-black">Privacy Policy</a>
                    <a href="#" class="text-gray-600 hover:text-black">Terms of Service</a>
                    <a href="#" class="text-gray-600 hover:text-black">Contact Us</a>
                </div>
            </div>

            <div class="text-center mt-8">
                <p class="text-gray-600">
                    Made with <i class="fas fa-heart text-red-500"></i> for UK Movers
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Sticky nav active link highlighting
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');

        window.addEventListener('scroll', () => {
            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;

                if (pageYOffset >= (sectionTop - 300)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').includes(current)) {
                    link.classList.add('active');
                }
            });
        });

        // View All Services functionality
        const viewAllBtn = document.getElementById('view-all-services');
        const hiddenServices = document.getElementById('hidden-services');

        viewAllBtn.addEventListener('click', () => {
            hiddenServices.classList.toggle('show-more');
            viewAllBtn.innerHTML = hiddenServices.classList.contains('show-more') ?
                'Show Less <i class="fas fa-arrow-up ml-2"></i>' :
                'View All <i class="fas fa-arrow-down ml-2"></i>';
        });

        // Explore More Cities functionality
        const exploreMoreBtn = document.getElementById('explore-more-cities');
        const hiddenCities = document.querySelectorAll('.hidden-cities');

        exploreMoreBtn.addEventListener('click', () => {
            hiddenCities.forEach(city => {
                city.classList.toggle('show-more');
            });
            exploreMoreBtn.innerHTML = hiddenCities[0].classList.contains('show-more') ?
                'Show Less Cities' :
                'Explore More Cities';
        });

        // Testimonial slider functionality
        const track = document.querySelector('.testimonial-track');
        const slides = Array.from(document.querySelectorAll('.testimonial-card'));
        const dots = Array.from(document.querySelectorAll('.testimonial-dot'));
        const prevBtn = document.querySelector('.testimonial-prev');
        const nextBtn = document.querySelector('.testimonial-next');

        const slideCount = slides.length;
        const slideWidth = slides[0].getBoundingClientRect().width;
        let currentSlide = 0;

        // Arrange slides next to each other
        const setSlidePosition = (slide, index) => {
            slide.style.left = `${slideWidth * index}px`;
        };
        slides.forEach(setSlidePosition);

        // Move to slide
        const moveToSlide = (slideIndex) => {
            track.style.transform = `translateX(-${slideWidth * slideIndex}px)`;
            currentSlide = slideIndex;

            // Update dots
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
        };

        // Click left arrow
        prevBtn.addEventListener('click', () => {
            currentSlide = (currentSlide - 1 + slideCount) % slideCount;
            moveToSlide(currentSlide);
        });

        // Click right arrow
        nextBtn.addEventListener('click', () => {
            currentSlide = (currentSlide + 1) % slideCount;
            moveToSlide(currentSlide);
        });

        // Click on dots
        dots.forEach(dot => {
            dot.addEventListener('click', () => {
                const slideIndex = parseInt(dot.getAttribute('data-index'));
                moveToSlide(slideIndex);
            });
        });

        // Auto slide
        let slideInterval = setInterval(() => {
            currentSlide = (currentSlide + 1) % slideCount;
            moveToSlide(currentSlide);
        }, 5000);

        // Pause on hover
        const slider = document.querySelector('.testimonial-container');
        slider.addEventListener('mouseenter', () => {
            clearInterval(slideInterval);
        });

        slider.addEventListener('mouseleave', () => {
            slideInterval = setInterval(() => {
                currentSlide = (currentSlide + 1) % slideCount;
                moveToSlide(currentSlide);
            }, 5000);
        });

        const mobileMenuButton = document.querySelector('.md\\:hidden');
        mobileMenuButton.addEventListener('click', () => {
            alert('Mobile menu would open here');
        });
    </script>
</body>

</html> -->

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~prompt - 2~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<!-- <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Globreo - UK Relocation Experts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #000000;
            --secondary: #ffffff;
            --accent: #f8fafc;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: var(--primary);
            background-color: var(--secondary);
            scroll-behavior: smooth;
        }

        .hero-bg {
            position: relative;
            overflow: hidden;
        }

        .uk-map-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://upload.wikimedia.org/wikipedia/commons/thumb/a/ae/United_Kingdom_%28orthographic_projection%29.svg/1200px-United_Kingdom_%28orthographic_projection%29.svg.png');
            background-size: cover;
            background-position: center;
            opacity: 0.15;
            animation: pulse 15s infinite alternate;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.15;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.2;
            }
            100% {
                transform: scale(1);
                opacity: 0.15;
            }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .sticky-nav {
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
        }

        .nav-link {
            position: relative;
        }

        .nav-link:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: var(--primary);
            transition: width 0.3s ease;
        }

        .nav-link:hover:after {
            width: 100%;
        }

        .nav-link.active:after {
            width: 100%;
        }

        .btn-magnetic {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-magnetic:hover {
            transform: scale(1.05);
        }

        .btn-magnetic:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .btn-magnetic:focus:after,
        .btn-magnetic:hover:after {
            animation: ripple 1s ease-out;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }

            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }

        .service-card {
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .service-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7));
        }

        .service-card-content {
            position: relative;
            z-index: 1;
        }

        .service-title-bg {
            backdrop-filter: blur(5px);
            background-color: rgba(255, 255, 255, 0.8);
        }

        .partner-logo {
            filter: grayscale(100%);
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .partner-logo:hover {
            filter: grayscale(0%);
            opacity: 1;
            transform: scale(1.1);
        }

        .city-card {
            transition: all 0.3s ease;
        }

        .city-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .toolkit-item {
            transition: all 0.3s ease;
        }

        .toolkit-item:hover {
            transform: translateY(-3px);
            background-color: #f8fafc;
        }

        .testimonial-slide {
            transition: all 0.5s ease;
        }

        .bounce-hover {
            transition: all 0.3s ease;
        }

        .bounce-hover:hover {
            transform: translateY(-3px);
        }

        .counter {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary);
        }

        .globe-logo {
            position: relative;
            display: inline-block;
        }

        .globe-logo:before {
            content: '';
            position: absolute;
            top: -2px;
            right: -8px;
            width: 12px;
            height: 12px;
            background-color: #ef4444;
            border-radius: 50%;
            transform: rotate(15deg);
        }

        /* New styles for improved sections */
        .service-icon-container {
            transition: all 0.3s ease;
        }

        .service-icon-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .hidden-services {
            display: none;
        }

        .show-more {
            display: grid;
        }

        .testimonial-container {
            position: relative;
            overflow: hidden;
        }

        .testimonial-track {
            display: flex;
            transition: transform 0.5s ease;
            width: 100%;
        }

        .testimonial-card {
            min-width: 100%;
            padding: 0 15px;
            box-sizing: border-box;
        }

        @media (min-width: 768px) {
            .testimonial-card {
                min-width: 50%;
            }
        }

        @media (min-width: 1024px) {
            .testimonial-card {
                min-width: 33.333%;
            }
        }

        .testimonial-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            z-index: 10;
        }

        .testimonial-prev {
            left: 10px;
        }

        .testimonial-next {
            right: 10px;
        }

        .testimonial-dots {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .testimonial-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ccc;
            margin: 0 5px;
            cursor: pointer;
        }

        .testimonial-dot.active {
            background: #000;
        }

        .hidden-cities {
            display: none;
        }

        .partner-logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            margin-bottom: 1rem;
        }

        .partner-name {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: #4b5563;
        }

        .partner-logo-img {
            width: 80px;
            height: 80px;
            object-fit: contain;
            filter: grayscale(100%);
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .partner-logo-img:hover {
            filter: grayscale(0%);
            opacity: 1;
            transform: scale(1.1);
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
            .hero-heading {
                font-size: 2rem;
            }
            
            .service-card, .city-card {
                height: 200px;
            }
            
            .partner-logo-img {
                width: 60px;
                height: 60px;
            }
            
            .testimonial-card {
                padding: 0 8px;
            }
        }
    </style>
</head>

<body>
    <nav class="sticky-nav py-4 px-6">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="relative">
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="18" fill="#000000" />
                        <path d="M20 2C15 2 10 5 7 9" stroke="white" stroke-width="2" />
                        <path d="M20 38C25 38 30 35 33 31" stroke="white" stroke-width="2" />
                        <path d="M2 20H38" stroke="white" stroke-width="2" />
                        <circle cx="20" cy="20" r="10" fill="#EF4444" />
                        <path d="M20 10V30" stroke="white" stroke-width="2" />
                        <path d="M10 20H30" stroke="white" stroke-width="2" />
                    </svg>
                </div>
                <span class="text-xl font-bold tracking-tight">GLOBREO</span>
            </div>
            <div class="hidden md:flex space-x-8">
                <a href="#services" class="nav-link">Services</a>
                <a href="#community" class="nav-link">Community Advice</a>
                <a href="#support" class="nav-link">Support</a>
                <a href="#jobs" class="nav-link">Jobs in UK</a>
                <a href="#legal" class="nav-link">Legal Help</a>
            </div>
            <div class="flex items-center space-x-4">
                <button
                    class="hidden md:flex items-center px-6 py-2 border border-black rounded-full hover:bg-black hover:text-white transition duration-300">
                    Login <i class="fas fa-arrow-right ml-2 text-sm"></i>
                </button>
                <button class="md:hidden">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </nav>

    <section class="hero-bg min-h-screen flex items-center justify-center text-center px-4">
        <div class="uk-map-animation"></div>
        <div class="hero-content container mx-auto max-w-5xl">
            <h1 class="hero-heading text-3xl md:text-4xl font-bold mb-6 leading-tight">
                <span class="block">Make the UK Your New Home</span>
                <span class="block">With Confidence & Support</span>
            </h1>
            <p class="text-lg md:text-xl mb-10 max-w-3xl mx-auto text-gray-700">
                Globreo is your one-stop UK relocation platform covering housing, education, finance, and local
                integration.
                We provide personalized guidance to make your transition smooth and successful.
            </p>
            <button
                class="btn-magnetic bg-black text-white px-10 py-4 rounded-full font-medium text-lg hover:bg-gray-800">
                Get Started <i class="fas fa-arrow-right ml-2"></i>
            </button>

            <div class="mt-20 flex flex-wrap justify-center gap-6 md:gap-10">
                <div class="flex flex-col items-center service-icon-container">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-home text-2xl text-blue-600"></i>
                    </div>
                    <span class="text-sm font-medium">Housing</span>
                </div>
                <div class="flex flex-col items-center service-icon-container">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-graduation-cap text-2xl text-purple-600"></i>
                    </div>
                    <span class="text-sm font-medium">Education</span>
                </div>
                <div class="flex flex-col items-center service-icon-container">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-pound-sign text-2xl text-green-600"></i>
                    </div>
                    <span class="text-sm font-medium">Finance</span>
                </div>
                <div class="flex flex-col items-center service-icon-container">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-users text-2xl text-orange-600"></i>
                    </div>
                    <span class="text-sm font-medium">Community</span>
                </div>
            </div>
        </div>
    </section>

    <section id="services" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold">Our Core Services</h2>
                <button id="view-all-services" class="text-black font-medium flex items-center">
                    View All <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Housing</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1588072432836-e10032774350?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Schools</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-**********-3a14e253d987?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Banking</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-*************-49953e39b3ac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2032&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Community Advice</h3>
                        </div>
                    </div>
                </div>
            </div>

            <div id="hidden-services" class="hidden-services grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-*************-7986c2920216?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Job Assistance</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1605106702734-205df224ecce?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">UK Visa Guidance</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Healthcare Registration</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl h-64"
                    style="background-image: url('https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Legal Support</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-16 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12">Our Trusted Partners</h2>

            <div class="flex flex-wrap justify-center items-center gap-8 md:gap-16">
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/a/aa/HSBC_logo_%282018%29.svg/2560px-HSBC_logo_%282018%29.svg.png" 
                         alt="HSBC" class="partner-logo-img">
                    <span class="partner-name">HSBC</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/de/Barclays_Bank_logo.svg/1200px-Barclays_Bank_logo.svg.png" 
                         alt="Barclays" class="partner-logo-img">
                    <span class="partner-name">Barclays</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/Rightmove_logo.svg/2560px-Rightmove_logo.svg.png" 
                         alt="Rightmove" class="partner-logo-img">
                    <span class="partner-name">Rightmove</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/4/4a/Gov.uk_logo.svg/1200px-Gov.uk_logo.svg.png" 
                         alt="GOV.UK" class="partner-logo-img">
                    <span class="partner-name">GOV.UK</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/7c/British_Council_logo.svg/2560px-British_Council_logo.svg.png" 
                         alt="British Council" class="partner-logo-img">
                    <span class="partner-name">British Council</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/03/NHS_England_logo.svg/1200px-NHS_England_logo.svg.png" 
                         alt="NHS" class="partner-logo-img">
                    <span class="partner-name">NHS</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/58/Uber_logo_2018.svg/2560px-Uber_logo_2018.svg.png" 
                         alt="Uber" class="partner-logo-img">
                    <span class="partner-name">Uber</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/8/8e/Vodafone_2017_logo.svg/2560px-Vodafone_2017_logo.svg.png" 
                         alt="Vodafone" class="partner-logo-img">
                    <span class="partner-name">Vodafone</span>
                </div>
            </div>
        </div>
    </section>

    <section id="cities" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold">Explore UK Cities</h2>
                <button id="explore-more-cities" class="text-black font-medium flex items-center">
                    View All <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="London" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">London</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££££ Expensive</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">The vibrant capital with endless opportunities and diverse
                            communities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,800</span>
                            <span><i class="fas fa-users mr-1"></i> 8.9M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                        alt="Manchester" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Manchester</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">A thriving northern city with great culture and affordable living.
                        </p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £950</span>
                            <span><i class="fas fa-users mr-1"></i> 2.8M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1508050919630-b135583b29ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Edinburgh" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Edinburgh</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's beautiful capital with historic charm and great
                            universities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,100</span>
                            <span><i class="fas fa-users mr-1"></i> 548,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden-cities city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1601823984263-b87b59798a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Birmingham" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Birmingham</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">England's second city with a thriving business scene and cultural
                            diversity.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £850</span>
                            <span><i class="fas fa-users mr-1"></i> 1.1M people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden-cities city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1571494146906-86de15d3817b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="Glasgow" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Glasgow</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's largest city with vibrant arts scene and friendly
                            locals.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £750</span>
                            <span><i class="fas fa-users mr-1"></i> 635,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden-cities city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1584696049838-8e2d9a3bfa8a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="Liverpool" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Liverpool</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Famous for its music heritage, waterfront and friendly locals.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £700</span>
                            <span><i class="fas fa-users mr-1"></i> 498,000 people</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4">UK Survival Toolkit</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Essential knowledge and resources to help you settle in the UK quickly and comfortably.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4 text-black">
                        <i class="fas fa-university"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Open a Bank Account</h3>
                    <p class="text-gray-600 text-sm">
                        Step-by-step guide to opening your first UK bank account as a newcomer.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4 text-black">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Get NHS Number</h3>
                    <p class="text-gray-600 text-sm">
                        How to register with a GP and access UK healthcare services.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4 text-black">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Essential Documents</h3>
                    <p class="text-gray-600 text-sm">
                        Checklist of documents needed for housing, schools, and employment.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4 text-black">
                        <i class="fas fa-train"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Public Transport</h3>
                    <p class="text-gray-600 text-sm">
                        Navigating buses, trains, and the London Underground system.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <button
                    class="bg-black text-white px-8 py-3 rounded-full font-medium hover:bg-gray-800 transition duration-300">
                    Download Full Toolkit (PDF)
                </button>
            </div>
        </div>
    </section>

    <section class="py-32 px-6 text-white relative" style="background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'); background-size: cover; background-position: center;">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Find Your Perfect Neighborhood in the UK</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Discover the best areas to live based on your lifestyle, budget, and family needs.
            </p>
            <button
                class="bounce-hover bg-white text-black px-10 py-4 rounded-full font-medium text-lg hover:bg-gray-100 shadow-lg">
                Get Started <i class="fas fa-search ml-2"></i>
            </button>
        </div>
    </section>

    <section class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4">What Our Customers Say</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Hear from families who have successfully relocated to the UK with our help.
            </p>

            <div class="testimonial-container relative max-w-6xl mx-auto">
                <div class="testimonial-track flex" id="testimonial-track">
                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="Sophie Martin"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Sophie Martin</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/fr.png" alt="France" class="w-5 mr-2">
                                        <span class="text-gray-600">From France • March 2023</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "Globreo made our move to London seamless. Their housing specialist found us the perfect
                                apartment in just two weeks. The schooling advice was invaluable for our children's
                                transition."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Raj Patel"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Raj Patel</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 mr-2">
                                        <span class="text-gray-600">From India • January 2023</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "As a single professional moving to Manchester, I didn't know where to start. Globreo's
                                relocation plan covered everything from visa support to finding the right neighborhood
                                for young professionals."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Elena Rodriguez"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Elena Rodriguez</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/es.png" alt="Spain" class="w-5 mr-2">
                                        <span class="text-gray-600">From Spain • November 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "The community adviser connected us with other Spanish families in Edinburgh before we
                                even arrived. Having that support network made all the difference in settling in."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star-half-alt text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Michael Johnson"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Michael Johnson</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/us.png" alt="USA" class="w-5 mr-2">
                                        <span class="text-gray-600">From USA • September 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "The legal support team helped navigate my work visa requirements with ease. They
                                anticipated every question I had before I even asked."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Aisha Mohammed"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Aisha Mohammed</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/ae.png" alt="UAE" class="w-5 mr-2">
                                        <span class="text-gray-600">From UAE • July 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "Finding halal food options and mosques in Birmingham was my biggest concern. Globreo's
                                cultural integration guide was exactly what I needed."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="far fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/men/55.jpg" alt="Wei Zhang"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Wei Zhang</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/cn.png" alt="China" class="w-5 mr-2">
                                        <span class="text-gray-600">From China • May 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "The Mandarin-speaking adviser helped my elderly parents feel comfortable with the
                                healthcare system. This personal touch meant everything."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="testimonial-nav testimonial-prev">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="testimonial-nav testimonial-next">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="testimonial-dots flex justify-center mt-8" id="testimonial-dots">
                <button class="testimonial-dot active" data-index="0"></button>
                <button class="testimonial-dot" data-index="1"></button>
                <button class="testimonial-dot" data-index="2"></button>
                <button class="testimonial-dot" data-index="3"></button>
                <button class="testimonial-dot" data-index="4"></button>
                <button class="testimonial-dot" data-index="5"></button>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-black text-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Begin Your UK Journey?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Get personalized relocation support tailored to your specific needs and circumstances.
            </p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <button
                    class="btn-magnetic bg-white text-black px-10 py-4 rounded-full font-medium hover:bg-gray-200 text-lg">
                    Get Started
                </button>
                <button
                    class="btn-magnetic border border-white text-white px-10 py-4 rounded-full font-medium hover:bg-gray-900 text-lg">
                    Speak to an Adviser
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-white py-12 px-6 border-t border-gray-200">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="relative">
                            <svg width="40" height="40" viewBox="0 0 40 40" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <circle cx="20" cy="20" r="18" fill="#000000" />
                                <path d="M20 2C15 2 10 5 7 9" stroke="white" stroke-width="2" />
                                <path d="M20 38C25 38 30 35 33 31" stroke="white" stroke-width="2" />
                                <path d="M2 20H38" stroke="white" stroke-width="2" />
                                <circle cx="20" cy="20" r="10" fill="#EF4444" />
                                <path d="M20 10V30" stroke="white" stroke-width="2" />
                                <path d="M10 20H30" stroke="white" stroke-width="2" />
                            </svg>
                        </div>
                        <span class="text-xl font-bold tracking-tight">GLOBREO</span>
                    </div>
                    <p class="text-gray-600 mb-4">
                        Helping individuals and families relocate to the UK with confidence since 2015.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#services" class="text-gray-600 hover:text-black">Housing Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Schooling Guidance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Visa Support</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Job Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Legal Support</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#cities" class="text-gray-600 hover:text-black">UK City Guides</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Cost of Living</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Relocation Checklist</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">UK Culture Guide</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Expat Community</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Newsletter</h3>
                    <p class="text-gray-600 mb-4">
                        Subscribe for relocation tips and UK updates.
                    </p>
                    <form class="flex">
                        <input type="email" placeholder="Your email"
                            class="px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-black w-full">
                        <button type="submit" class="bg-black text-white px-4 py-3 rounded-r-lg hover:bg-gray-800">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-600 mb-4 md:mb-0">
                    © 2023 Globreo. All rights reserved.
                </p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-600 hover:text-black">Privacy Policy</a>
                    <a href="#" class="text-gray-600 hover:text-black">Terms of Service</a>
                    <a href="#" class="text-gray-600 hover:text-black">Contact Us</a>
                </div>
            </div>

            <div class="text-center mt-8">
                <p class="text-gray-600">
                    Made with <i class="fas fa-heart text-red-500"></i> for UK Movers
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Sticky nav active link highlighting
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');

        window.addEventListener('scroll', () => {
            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;

                if (pageYOffset >= (sectionTop - 300)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').includes(current)) {
                    link.classList.add('active');
                }
            });
        });

        // View All Services functionality
        const viewAllBtn = document.getElementById('view-all-services');
        const hiddenServices = document.getElementById('hidden-services');

        viewAllBtn.addEventListener('click', () => {
            hiddenServices.classList.toggle('show-more');
            viewAllBtn.innerHTML = hiddenServices.classList.contains('show-more') ?
                'Show Less <i class="fas fa-arrow-up ml-2"></i>' :
                'View All <i class="fas fa-arrow-right ml-2"></i>';
        });

        // Explore More Cities functionality
        const exploreMoreBtn = document.getElementById('explore-more-cities');
        const hiddenCities = document.querySelectorAll('.hidden-cities');

        exploreMoreBtn.addEventListener('click', () => {
            hiddenCities.forEach(city => {
                city.classList.toggle('show-more');
            });
            exploreMoreBtn.innerHTML = hiddenCities[0].classList.contains('show-more') ?
                'Show Less <i class="fas fa-arrow-up ml-2"></i>' :
                'View All <i class="fas fa-arrow-right ml-2"></i>';
        });

        // Testimonial slider functionality with infinite loop
        const track = document.querySelector('.testimonial-track');
        const slides = Array.from(document.querySelectorAll('.testimonial-card'));
        const dots = Array.from(document.querySelectorAll('.testimonial-dot'));
        const prevBtn = document.querySelector('.testimonial-prev');
        const nextBtn = document.querySelector('.testimonial-next');

        const slideCount = slides.length;
        const slideWidth = slides[0].getBoundingClientRect().width;
        let currentSlide = 0;
        let isTransitioning = false;

        // Clone first and last slides for infinite effect
        const firstSlide = slides[0].cloneNode(true);
        const lastSlide = slides[slideCount - 1].cloneNode(true);
        track.appendChild(firstSlide);
        track.insertBefore(lastSlide, slides[0]);

        // Arrange slides next to each other
        const setSlidePosition = (slide, index) => {
            slide.style.left = `${slideWidth * index}px`;
        };
        slides.forEach(setSlidePosition);

        // Update track position to show first "real" slide (not the cloned last one)
        track.style.transform = `translateX(-${slideWidth}px)`;

        // Move to slide with infinite loop effect
        const moveToSlide = (slideIndex) => {
            if (isTransitioning) return;
            isTransitioning = true;
            
            track.style.transition = 'transform 0.5s ease';
            track.style.transform = `translateX(-${slideWidth * (slideIndex + 1)}px)`;
            currentSlide = slideIndex;

            // Update dots
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });

            setTimeout(() => {
                isTransitioning = false;
            }, 500);
        };

        // Handle transition end for infinite loop
        track.addEventListener('transitionend', () => {
            // If we're at the cloned first slide (after last real slide)
            if (currentSlide === slideCount) {
                track.style.transition = 'none';
                track.style.transform = `translateX(-${slideWidth}px)`;
                currentSlide = 0;
            }
            // If we're at the cloned last slide (before first real slide)
            else if (currentSlide === -1) {
                track.style.transition = 'none';
                track.style.transform = `translateX(-${slideWidth * slideCount}px)`;
                currentSlide = slideCount - 1;
            }
        });

        // Click left arrow
        prevBtn.addEventListener('click', () => {
            moveToSlide((currentSlide - 1 + slideCount) % slideCount);
        });

        // Click right arrow
        nextBtn.addEventListener('click', () => {
            moveToSlide((currentSlide + 1) % slideCount);
        });

        // Click on dots
        dots.forEach(dot => {
            dot.addEventListener('click', () => {
                const slideIndex = parseInt(dot.getAttribute('data-index'));
                moveToSlide(slideIndex);
            });
        });

        // Auto slide
        let slideInterval = setInterval(() => {
            moveToSlide((currentSlide + 1) % slideCount);
        }, 5000);

        // Pause on hover
        const slider = document.querySelector('.testimonial-container');
        slider.addEventListener('mouseenter', () => {
            clearInterval(slideInterval);
        });

        slider.addEventListener('mouseleave', () => {
            slideInterval = setInterval(() => {
                moveToSlide((currentSlide + 1) % slideCount);
            }, 5000);
        });

        // Mobile menu toggle (would need implementation)
        const mobileMenuButton = document.querySelector('.md\\:hidden');
        mobileMenuButton.addEventListener('click', () => {
            // This would need a mobile menu implementation
            alert('Mobile menu would open here');
        });
    </script>
</body>

</html> -->
<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Globreo - UK Relocation Experts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4441da;
            --primary-light: #6a67e2;
            --primary-dark: #2e2cb3;
            --secondary: #ffffff;
            --accent: #f8fafc;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #1a1a1a;
            background-color: var(--secondary);
            scroll-behavior: smooth;
        }

        /* Animated gradient text (left to right) */
        .gradient-text {
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
            background-size: 300% 100%;
            background-image: linear-gradient(90deg, #4441da, #6a67e2, #8b5cf6, #4441da);
            animation: gradient 6s linear infinite;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }

        /* Button styles */
        .btn-gradient {
            background: linear-gradient(90deg, #4441da, #6a67e2);
            color: white;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(68, 65, 218, 0.3);
        }

        .btn-gradient:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(68, 65, 218, 0.4);
        }

        .btn-gradient:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #6a67e2, #8b5cf6);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .btn-gradient:hover:after {
            opacity: 1;
        }

        .btn-gradient span {
            position: relative;
            z-index: 1;
        }

        /* Hero section */
        .hero-bg {
            position: relative;
            overflow: hidden;
            background: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.95)), 
                        url('https://img.freepik.com/free-photo/london-big-ben-river-thames-sunset-uk_181624-26858.jpg') no-repeat center center;
            background-size: cover;
        }

        .hero-heading {
            font-size: 4rem;
            line-height: 1.1;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .hero-heading span {
            display: inline-block;
            position: relative;
        }

        .hero-heading span:after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #4441da, #6a67e2);
            transform: scaleX(0);
            transform-origin: left;
            animation: underline 1.5s ease-in-out forwards;
            animation-delay: 0.5s;
        }

        @keyframes underline {
            to { transform: scaleX(1); }
        }

        /* Service cards */
        .service-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            height: 320px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        }

        .service-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .service-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.5));
            z-index: 1;
        }

        .service-card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .service-title-bg {
            backdrop-filter: blur(5px);
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-title-bg {
            background-color: rgba(68, 65, 218, 0.9);
        }

        .service-card:hover .service-title-bg h3 {
            color: white;
        }

        /* Floating animation for service icons */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        .floating-icon {
            animation: float 6s ease-in-out infinite;
        }

        .floating-icon:nth-child(2) {
            animation-delay: 1s;
        }

        .floating-icon:nth-child(3) {
            animation-delay: 2s;
        }

        .floating-icon:nth-child(4) {
            animation-delay: 3s;
        }

        /* Sticky nav */
        .sticky-nav {
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
        }

        .nav-link {
            position: relative;
            color: #1a1a1a;
            transition: color 0.3s ease;
        }

        .nav-link:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: var(--primary);
            transition: width 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        .nav-link:hover:after {
            width: 100%;
        }

        .nav-link.active {
            color: var(--primary);
        }

        .nav-link.active:after {
            width: 100%;
        }

        /* Partner logos */
        .partner-logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            margin-bottom: 1rem;
        }

        .partner-name {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: #4b5563;
        }

        .partner-logo-img {
            width: 80px;
            height: 80px;
            object-fit: contain;
            filter: grayscale(100%);
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .partner-logo-img:hover {
            filter: grayscale(0%);
            opacity: 1;
            transform: scale(1.1);
        }

        /* City cards */
        .city-card {
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            overflow: hidden;
        }

        .city-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Toolkit items */
        .toolkit-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            background-color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .toolkit-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(68, 65, 218, 0.15);
        }

        .toolkit-item i {
            color: var(--primary);
        }

        /* Testimonial slider */
        .testimonial-container {
            position: relative;
            overflow: hidden;
        }

        .testimonial-track {
            display: flex;
            transition: transform 0.5s ease;
            width: 100%;
        }

        .testimonial-card {
            min-width: 100%;
            padding: 0 15px;
            box-sizing: border-box;
        }

        @media (min-width: 768px) {
            .testimonial-card {
                min-width: 50%;
            }
        }

        @media (min-width: 1024px) {
            .testimonial-card {
                min-width: 33.333%;
            }
        }

        .testimonial-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
        }

        .testimonial-nav:hover {
            transform: translateY(-50%) scale(1.1);
            background: var(--primary);
            color: white;
        }

        .testimonial-prev {
            left: 10px;
        }

        .testimonial-next {
            right: 10px;
        }

        .testimonial-dots {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .testimonial-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ccc;
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .testimonial-dot.active {
            background: var(--primary);
            transform: scale(1.2);
        }

        /* Counter animation */
        .counter {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary);
        }

        /* Globe logo */
        .globe-logo {
            position: relative;
            display: inline-block;
        }

        .globe-logo:before {
            content: '';
            position: absolute;
            top: -2px;
            right: -8px;
            width: 12px;
            height: 12px;
            background-color: #ef4444;
            border-radius: 50%;
            transform: rotate(15deg);
            animation: pulse 2s infinite;
        }

        /* Fade-in animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 1s ease forwards;
        }

        .fade-in-delay-1 {
            animation: fadeIn 1s ease 0.3s forwards;
            opacity: 0;
        }

        .fade-in-delay-2 {
            animation: fadeIn 1s ease 0.6s forwards;
            opacity: 0;
        }

        .fade-in-delay-3 {
            animation: fadeIn 1s ease 0.9s forwards;
            opacity: 0;
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
            .hero-heading {
                font-size: 2.5rem;
            }
            
            .service-card {
                height: 250px;
            }
            
            .partner-logo-img {
                width: 60px;
                height: 60px;
            }
            
            .testimonial-card {
                padding: 0 8px;
            }
        }
    </style>
</head>

<body>
    <nav class="sticky-nav py-4 px-6 fade-in">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="globe-logo">
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="18" fill="#4441da" />
                        <path d="M20 2C15 2 10 5 7 9" stroke="white" stroke-width="2" />
                        <path d="M20 38C25 38 30 35 33 31" stroke="white" stroke-width="2" />
                        <path d="M2 20H38" stroke="white" stroke-width="2" />
                        <circle cx="20" cy="20" r="10" fill="#EF4444" />
                        <path d="M20 10V30" stroke="white" stroke-width="2" />
                        <path d="M10 20H30" stroke="white" stroke-width="2" />
                    </svg>
                </div>
                <span class="text-xl font-bold tracking-tight">GLOBREO</span>
            </div>
            <div class="hidden md:flex space-x-8">
                <a href="#services" class="nav-link">Services</a>
                <a href="#community" class="nav-link">Community Advice</a>
                <a href="#support" class="nav-link">Support</a>
                <a href="#jobs" class="nav-link">Jobs in UK</a>
                <a href="#legal" class="nav-link">Legal Help</a>
            </div>
            <div class="flex items-center space-x-4">
                <button
                    class="hidden md:flex items-center px-6 py-2 border border-black rounded-full hover:bg-black hover:text-white transition duration-300">
                    Login <i class="fas fa-arrow-right ml-2 text-sm"></i>
                </button>
                <button class="md:hidden">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </nav>

    <section class="hero-bg min-h-screen flex items-center justify-center text-center px-4">
        <div class="hero-content container mx-auto max-w-5xl">
            <h1 class="hero-heading text-5xl md:text-6xl font-bold mb-6 leading-tight">
                <span class="block gradient-text fade-in">Make the UK Your New Home</span>
                <span class="block gradient-text fade-in-delay-1">With Confidence & Support</span>
            </h1>
            <p class="text-lg md:text-xl mb-10 max-w-3xl mx-auto text-gray-700 fade-in-delay-2">
                Globreo is your one-stop UK relocation platform covering housing, education, finance, and local
                integration. We provide personalized guidance to make your transition smooth and successful.
            </p>
            <button
                class="btn-gradient px-12 py-4 rounded-full font-medium text-lg relative overflow-hidden fade-in-delay-3">
                <span>Get Started <i class="fas fa-arrow-right ml-2"></i></span>
            </button>

            <div class="mt-20 flex flex-wrap justify-center gap-6 md:gap-10">
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-home text-2xl" style="color: #4441da;"></i>
                    </div>
                    <span class="text-sm font-medium">Housing</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-graduation-cap text-2xl" style="color: #4441da;"></i>
                    </div>
                    <span class="text-sm font-medium">Education</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-pound-sign text-2xl" style="color: #4441da;"></i>
                    </div>
                    <span class="text-sm font-medium">Finance</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div
                        class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300">
                        <i class="fas fa-users text-2xl" style="color: #4441da;"></i>
                    </div>
                    <span class="text-sm font-medium">Community</span>
                </div>
            </div>
        </div>
    </section>

    <section id="services" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold gradient-text">Our Core Services</h2>
                <button id="view-all-services" class="text-black font-medium flex items-center">
                    View All <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/house-isolated-field_1303-23773.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Housing</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/group-diverse-kids-studying-together_53876-95845.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Schools</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/business-people-working-finance-accounting-analyze-financi_74952-1379.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Banking</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/group-diverse-people-having-business-meeting_53876-95060.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Community Advice</h3>
                        </div>
                    </div>
                </div>
            </div>

            <div id="hidden-services" class="hidden grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/business-people-shaking-hands-together_53876-30588.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Job Assistance</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/immigration-concept-with-passport_23-2149707876.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">UK Visa Guidance</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/doctor-with-his-arms-crossed-white-background_1368-5790.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Healthcare Registration</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/lawyer-explaining-clients-their-rights_74855-5261.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Legal Support</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-16 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12 gradient-text">Our Trusted Partners</h2>

            <div class="flex flex-wrap justify-center items-center gap-8 md:gap-16">
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/a/aa/HSBC_logo_%282018%29.svg/2560px-HSBC_logo_%282018%29.svg.png" 
                         alt="HSBC" class="partner-logo-img">
                    <span class="partner-name">HSBC</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/de/Barclays_Bank_logo.svg/1200px-Barclays_Bank_logo.svg.png" 
                         alt="Barclays" class="partner-logo-img">
                    <span class="partner-name">Barclays</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/Rightmove_logo.svg/2560px-Rightmove_logo.svg.png" 
                         alt="Rightmove" class="partner-logo-img">
                    <span class="partner-name">Rightmove</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/4/4a/Gov.uk_logo.svg/1200px-Gov.uk_logo.svg.png" 
                         alt="GOV.UK" class="partner-logo-img">
                    <span class="partner-name">GOV.UK</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/7c/British_Council_logo.svg/2560px-British_Council_logo.svg.png" 
                         alt="British Council" class="partner-logo-img">
                    <span class="partner-name">British Council</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/03/NHS_England_logo.svg/1200px-NHS_England_logo.svg.png" 
                         alt="NHS" class="partner-logo-img">
                    <span class="partner-name">NHS</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/58/Uber_logo_2018.svg/2560px-Uber_logo_2018.svg.png" 
                         alt="Uber" class="partner-logo-img">
                    <span class="partner-name">Uber</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/8/8e/Vodafone_2017_logo.svg/2560px-Vodafone_2017_logo.svg.png" 
                         alt="Vodafone" class="partner-logo-img">
                    <span class="partner-name">Vodafone</span>
                </div>
            </div>
        </div>
    </section>

    <section id="cities" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold gradient-text">Explore UK Cities</h2>
                <button id="explore-more-cities" class="text-black font-medium flex items-center">
                    View All <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="London" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">London</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££££ Expensive</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">The vibrant capital with endless opportunities and diverse
                            communities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,800</span>
                            <span><i class="fas fa-users mr-1"></i> 8.9M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                        alt="Manchester" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Manchester</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">A thriving northern city with great culture and affordable living.
                        </p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £950</span>
                            <span><i class="fas fa-users mr-1"></i> 2.8M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1508050919630-b135583b29ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Edinburgh" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Edinburgh</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's beautiful capital with historic charm and great
                            universities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,100</span>
                            <span><i class="fas fa-users mr-1"></i> 548,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1601823984263-b87b59798a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Birmingham" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Birmingham</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">England's second city with a thriving business scene and cultural
                            diversity.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £850</span>
                            <span><i class="fas fa-users mr-1"></i> 1.1M people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1571494146906-86de15d3817b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="Glasgow" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Glasgow</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's largest city with vibrant arts scene and friendly
                            locals.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £750</span>
                            <span><i class="fas fa-users mr-1"></i> 635,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1584696049838-8e2d9a3bfa8a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="Liverpool" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Liverpool</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Famous for its music heritage, waterfront and friendly locals.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £700</span>
                            <span><i class="fas fa-users mr-1"></i> 498,000 people</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 gradient-text">UK Survival Toolkit</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Essential knowledge and resources to help you settle in the UK quickly and comfortably.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4">
                        <i class="fas fa-university"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Open a Bank Account</h3>
                    <p class="text-gray-600 text-sm">
                        Step-by-step guide to opening your first UK bank account as a newcomer.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Get NHS Number</h3>
                    <p class="text-gray-600 text-sm">
                        How to register with a GP and access UK healthcare services.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Essential Documents</h3>
                    <p class="text-gray-600 text-sm">
                        Checklist of documents needed for housing, schools, and employment.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4">
                        <i class="fas fa-train"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Public Transport</h3>
                    <p class="text-gray-600 text-sm">
                        Navigating buses, trains, and the London Underground system.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <button
                    class="btn-gradient px-10 py-3 rounded-full font-medium text-lg relative overflow-hidden">
                    <span>Download Full Toolkit (PDF)</span>
                </button>
            </div>
        </div>
    </section>

    <section class="py-32 px-6 text-white relative" style="background-image: linear-gradient(rgba(68, 65, 218, 0.8), rgba(68, 65, 218, 0.9)), url('https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'); background-size: cover; background-position: center;">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6 gradient-text">Find Your Perfect Neighborhood in the UK</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Discover the best areas to live based on your lifestyle, budget, and family needs.
            </p>
            <button
                class="bg-white text-primary px-10 py-4 rounded-full font-medium text-lg hover:bg-gray-100 shadow-lg">
                Get Started <i class="fas fa-search ml-2"></i>
            </button>
        </div>
    </section>

    <section class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 gradient-text">What Our Customers Say</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Hear from families who have successfully relocated to the UK with our help.
            </p>

            <div class="testimonial-container relative max-w-6xl mx-auto">
                <div class="testimonial-track flex" id="testimonial-track">
                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="Sophie Martin"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Sophie Martin</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/fr.png" alt="France" class="w-5 mr-2">
                                        <span class="text-gray-600">From France • March 2023</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "Globreo made our move to London seamless. Their housing specialist found us the perfect
                                apartment in just two weeks. The schooling advice was invaluable for our children's
                                transition."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Raj Patel"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Raj Patel</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 mr-2">
                                        <span class="text-gray-600">From India • January 2023</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "As a single professional moving to Manchester, I didn't know where to start. Globreo's
                                relocation plan covered everything from visa support to finding the right neighborhood
                                for young professionals."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Elena Rodriguez"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Elena Rodriguez</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/es.png" alt="Spain" class="w-5 mr-2">
                                        <span class="text-gray-600">From Spain • November 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "The community adviser connected us with other Spanish families in Edinburgh before we
                                even arrived. Having that support network made all the difference in settling in."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star-half-alt text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Michael Johnson"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Michael Johnson</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/us.png" alt="USA" class="w-5 mr-2">
                                        <span class="text-gray-600">From USA • September 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "The legal support team helped navigate my work visa requirements with ease. They
                                anticipated every question I had before I even asked."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Aisha Mohammed"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Aisha Mohammed</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/ae.png" alt="UAE" class="w-5 mr-2">
                                        <span class="text-gray-600">From UAE • July 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "Finding halal food options and mosques in Birmingham was my biggest concern. Globreo's
                                cultural integration guide was exactly what I needed."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="far fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card px-4">
                        <div class="bg-gray-50 p-8 rounded-xl h-full">
                            <div class="flex items-center mb-6">
                                <img src="https://randomuser.me/api/portraits/men/55.jpg" alt="Wei Zhang"
                                    class="w-16 h-16 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-bold">Wei Zhang</h4>
                                    <div class="flex items-center mt-1">
                                        <img src="https://flagcdn.com/w20/cn.png" alt="China" class="w-5 mr-2">
                                        <span class="text-gray-600">From China • May 2022</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-700 italic">
                                "The Mandarin-speaking adviser helped my elderly parents feel comfortable with the
                                healthcare system. This personal touch meant everything."
                            </p>
                            <div class="mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="testimonial-nav testimonial-prev">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="testimonial-nav testimonial-next">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="testimonial-dots flex justify-center mt-8" id="testimonial-dots">
                <button class="testimonial-dot active" data-index="0"></button>
                <button class="testimonial-dot" data-index="1"></button>
                <button class="testimonial-dot" data-index="2"></button>
                <button class="testimonial-dot" data-index="3"></button>
                <button class="testimonial-dot" data-index="4"></button>
                <button class="testimonial-dot" data-index="5"></button>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-primary text-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6 gradient-text">Ready to Begin Your UK Journey?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Get personalized relocation support tailored to your specific needs and circumstances.
            </p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <button
                    class="btn-gradient px-12 py-4 rounded-full font-medium text-lg relative overflow-hidden">
                    <span>Get Started</span>
                </button>
                <button
                    class="border border-white text-white px-12 py-4 rounded-full font-medium hover:bg-white hover:text-primary text-lg transition duration-300">
                    Speak to an Adviser
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-white py-12 px-6 border-t border-gray-200">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="globe-logo">
                            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="20" cy="20" r="18" fill="#4441da" />
                                <path d="M20 2C15 2 10 5 7 9" stroke="white" stroke-width="2" />
                                <path d="M20 38C25 38 30 35 33 31" stroke="white" stroke-width="2" />
                                <path d="M2 20H38" stroke="white" stroke-width="2" />
                                <circle cx="20" cy="20" r="10" fill="#EF4444" />
                                <path d="M20 10V30" stroke="white" stroke-width="2" />
                                <path d="M10 20H30" stroke="white" stroke-width="2" />
                            </svg>
                        </div>
                        <span class="text-xl font-bold tracking-tight">GLOBREO</span>
                    </div>
                    <p class="text-gray-600 mb-4">
                        Helping individuals and families relocate to the UK with confidence since 2015.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-600 hover:text-primary"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-600 hover:text-primary"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-600 hover:text-primary"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-600 hover:text-primary"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#services" class="text-gray-600 hover:text-primary">Housing Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-primary">Schooling Guidance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-primary">Visa Support</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-primary">Job Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-primary">Legal Support</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#cities" class="text-gray-600 hover:text-primary">UK City Guides</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">Cost of Living</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">Relocation Checklist</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">UK Culture Guide</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">Expat Community</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Newsletter</h3>
                    <p class="text-gray-600 mb-4">
                        Subscribe for relocation tips and UK updates.
                    </p>
                    <form class="flex">
                        <input type="email" placeholder="Your email"
                            class="px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-primary w-full">
                        <button type="submit" class="bg-primary text-white px-4 py-3 rounded-r-lg hover:bg-primary-dark">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-600 mb-4 md:mb-0">
                    © 2023 Globreo. All rights reserved.
                </p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-600 hover:text-primary">Privacy Policy</a>
                    <a href="#" class="text-gray-600 hover:text-primary">Terms of Service</a>
                    <a href="#" class="text-gray-600 hover:text-primary">Contact Us</a>
                </div>
            </div>

            <div class="text-center mt-8">
                <p class="text-gray-600">
                    Made with <i class="fas fa-heart text-red-500"></i> for UK Movers
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Sticky nav active link highlighting
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');

        window.addEventListener('scroll', () => {
            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;

                if (window.pageYOffset >= (sectionTop - 300)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').includes(current)) {
                    link.classList.add('active');
                }
            });
        });

        // View All Services functionality
        const viewAllBtn = document.getElementById('view-all-services');
        const hiddenServices = document.getElementById('hidden-services');

        viewAllBtn.addEventListener('click', () => {
            hiddenServices.classList.toggle('hidden');
            viewAllBtn.innerHTML = hiddenServices.classList.contains('hidden') ?
                'View All <i class="fas fa-arrow-right ml-2"></i>' :
                'Show Less <i class="fas fa-arrow-up ml-2"></i>';
        });

        // Explore More Cities functionality
        const exploreMoreBtn = document.getElementById('explore-more-cities');
        const hiddenCities = document.querySelectorAll('.hidden');

        exploreMoreBtn.addEventListener('click', () => {
            hiddenCities.forEach(city => {
                if (city.classList.contains('city-card')) {
                    city.classList.toggle('hidden');
                }
            });
            exploreMoreBtn.innerHTML = !hiddenCities[0].classList.contains('hidden') ?
                'Show Less <i class="fas fa-arrow-up ml-2"></i>' :
                'View All <i class="fas fa-arrow-right ml-2"></i>';
        });

        // Testimonial slider functionality with infinite loop
        const track = document.getElementById('testimonial-track');
        const slides = Array.from(document.querySelectorAll('.testimonial-card'));
        const dots = Array.from(document.querySelectorAll('.testimonial-dot'));
        const prevBtn = document.querySelector('.testimonial-prev');
        const nextBtn = document.querySelector('.testimonial-next');

        const slideCount = slides.length;
        let currentSlide = 0;
        let isTransitioning = false;

        // Set initial positions
        const setSlidePositions = () => {
            const slideWidth = slides[0].getBoundingClientRect().width;
            slides.forEach((slide, index) => {
                slide.style.left = `${slideWidth * index}px`;
            });
        };

        // Update track position to show current slide
        const moveToSlide = (slideIndex) => {
            if (isTransitioning) return;
            isTransitioning = true;
            
            const slideWidth = slides[0].getBoundingClientRect().width;
            track.style.transition = 'transform 0.5s ease';
            track.style.transform = `translateX(-${slideWidth * (slideIndex + 1)}px)`;
            currentSlide = slideIndex;

            // Update dots
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });

            setTimeout(() => {
                isTransitioning = false;
            }, 500);
        };

        // Handle transition end for infinite loop
        track.addEventListener('transitionend', () => {
            const slideWidth = slides[0].getBoundingClientRect().width;
            
            // If we're at the cloned first slide (after last real slide)
            if (currentSlide === slideCount - 1) {
                track.style.transition = 'none';
                track.style.transform = `translateX(-${slideWidth}px)`;
                currentSlide = 0;
            }
            // If we're at the cloned last slide (before first real slide)
            else if (currentSlide === -1) {
                track.style.transition = 'none';
                track.style.transform = `translateX(-${slideWidth * slideCount}px)`;
                currentSlide = slideCount - 1;
            }
        });

        // Click left arrow
        prevBtn.addEventListener('click', () => {
            moveToSlide((currentSlide - 1 + slideCount) % slideCount);
        });

        // Click right arrow
        nextBtn.addEventListener('click', () => {
            moveToSlide((currentSlide + 1) % slideCount);
        });

        // Click on dots
        dots.forEach(dot => {
            dot.addEventListener('click', () => {
                const slideIndex = parseInt(dot.getAttribute('data-index'));
                moveToSlide(slideIndex);
            });
        });

        // Auto slide
        let slideInterval = setInterval(() => {
            moveToSlide((currentSlide + 1) % slideCount);
        }, 5000);

        // Pause on hover
        const slider = document.querySelector('.testimonial-container');
        slider.addEventListener('mouseenter', () => {
            clearInterval(slideInterval);
        });

        slider.addEventListener('mouseleave', () => {
            slideInterval = setInterval(() => {
                moveToSlide((currentSlide + 1) % slideCount);
            }, 5000);
        });

        // Initialize slider
        window.addEventListener('load', () => {
            setSlidePositions();
            moveToSlide(0);
        });

        // Mobile menu toggle (would need implementation)
        const mobileMenuButton = document.querySelector('.md\\:hidden');
        mobileMenuButton.addEventListener('click', () => {
            // This would need a mobile menu implementation
            alert('Mobile menu would open here');
        });
    </script>
</body>
</html> -->


<!-- prompt-4 -->

<!-- <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MUVEAZY - UK Relocation Experts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            color: #1a1a1a;
            background-color: #ffffff;
        }

        .hero-bg {
            position: relative;
            overflow: hidden;
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.6)),
                url('https://img.freepik.com/premium-photo/london-tower-bridge-thames-river_79295-6823.jpg?w=1380') no-repeat center center;
            background-size: cover;
            color: white;
        }

        .uk-flag {
            display: inline-block;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #012169 0%, #012169 33%, #C8102E 33%, #C8102E 66%, #FFFFFF 66%, #FFFFFF 100%);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            margin-left: 15px;
            vertical-align: middle;
            transform: translateY(-5px);
            animation: floatFlag 4s ease-in-out infinite;
        }

        @keyframes floatFlag {
            0% {
                transform: translateY(-5px) rotate(0deg);
            }

            50% {
                transform: translateY(-15px) rotate(5deg);
            }

            100% {
                transform: translateY(-5px) rotate(0deg);
            }
        }

        .floating-icon {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0px) rotateY(0deg);
            }

            50% {
                transform: translateY(-15px) rotateY(15deg);
            }

            100% {
                transform: translateY(0px) rotateY(0deg);
            }
        }

        .service-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            height: 350px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        }

        .service-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.5));
            z-index: 1;
        }

        .service-card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .service-title-bg {
            backdrop-filter: blur(5px);
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-title-bg {
            background-color: #4441da;
        }

        .service-card:hover .service-title-bg h3 {
            color: white;
        }

        .neighborhood-section {
            position: relative;
            padding: 80px 0;
            background: linear-gradient(rgba(68, 65, 218, 0.8), rgba(68, 65, 218, 0.9)),
                url('https://img.freepik.com/premium-photo/london-tower-bridge-thames-river_79295-6823.jpg?w=1380');
            background-size: cover;
            background-position: center;
            color: white;
        }

        .neighborhood-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 100px;
        }

        .neighborhood-image {
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .neighborhood-image:hover {
            transform: scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        .testimonial-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        @media (max-width: 1024px) {
            .testimonial-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .neighborhood-container {
                padding: 0 50px;
            }
        }

        @media (max-width: 768px) {
            .testimonial-grid {
                grid-template-columns: 1fr;
            }

            .neighborhood-container {
                padding: 0 30px;
            }

            .hero-heading {
                font-size: 2.5rem;
            }

            .service-card {
                height: 280px;
            }
        }

        @media (max-width: 640px) {
            .neighborhood-container {
                padding: 0 20px;
            }
        }

        .btn-black {
            background: #000000;
            color: white;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .btn-black:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            background: #333333;
        }

        .logo-m {
            color: #4441da;
            font-weight: 800;
            font-size: 24px;
        }
    </style>
</head>

<body>
    <nav class="sticky-nav py-4 px-6 fade-in bg-white shadow-sm">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <span class="text-xl font-bold tracking-tight"><span class="logo-m">M</span>UVEAZY</span>
            </div>
            <div class="hidden md:flex space-x-8">
                <a href="#services" class="nav-link text-black hover:text-gray-600">Services</a>
                <a href="#community" class="nav-link text-black hover:text-gray-600">Community Advice</a>
                <a href="#support" class="nav-link text-black hover:text-gray-600">Support</a>
                <a href="#jobs" class="nav-link text-black hover:text-gray-600">Jobs in UK</a>
                <a href="#legal" class="nav-link text-black hover:text-gray-600">Legal Help</a>
            </div>
            <div class="flex items-center space-x-4">
                <button class="hidden md:flex items-center px-6 py-2 btn-black rounded-full font-medium">
                    Login <i class="fas fa-arrow-right ml-2 text-sm"></i>
                </button>
                <button class="md:hidden">
                    <i class="fas fa-bars text-xl text-black"></i>
                </button>
            </div>
        </div>
    </nav>

    <section class="hero-bg min-h-screen flex items-center justify-center text-center px-4">
        <div class="hero-content container mx-auto max-w-5xl">
            <h1 class="hero-heading text-5xl md:text-6xl font-bold mb-6 leading-tight text-white">
                <span class="block fade-in">Make the UK Your New Home</span>
                <span class="block fade-in-delay-1">With Confidence & Support</span>
                <span class="uk-flag"></span>
            </h1>
            <p class="text-lg md:text-xl mb-10 max-w-3xl mx-auto text-gray-200 fade-in-delay-2">
                MUVEAZY is your one-stop UK relocation platform covering housing, education, finance, and local
                integration. We provide personalized guidance to make your transition smooth and successful.
            </p>
            <button
                class="btn-black px-12 py-4 rounded-full font-medium text-lg relative overflow-hidden fade-in-delay-3">
                <span>Get Started <i class="fas fa-arrow-right ml-2"></i></span>
            </button>

            <div class="mt-20 flex flex-wrap justify-center gap-6 md:gap-10">
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #FF5733;">
                        <i class="fas fa-home text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Housing</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #33FF57;">
                        <i class="fas fa-graduation-cap text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Education</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #3357FF;">
                        <i class="fas fa-pound-sign text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Finance</span>
                </div>

                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #E151FF;">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Community</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #FFBF00;">
                        <i class="fas fa-briefcase text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Jobs</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #714DFF;">
                        <i class="fas fa-balance-scale text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Legal</span>
                </div>
            </div>
        </div>
    </section>

    <section id="services" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-black">Our Core Services</h2>
                <button id="view-all-services" class="text-black font-medium flex items-center">
                    View All <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/front-view-front-door-with-white-wall-plants_23-2149360608.jpg?t=st=1747978103~exp=1747981703~hmac=da2efafbfd8b4c29c6ece6c3ab503f39361299d29cc5399ba63a89ea6ee3ad23&w=740')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Housing</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/group-diverse-kids-studying-together_53876-95845.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Schools</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/business-people-working-finance-accounting-analyze-financi_74952-1379.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Banking</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/group-diverse-people-having-business-meeting_53876-95060.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Community Advice</h3>
                        </div>
                    </div>
                </div>
            </div>

            <div id="hidden-services" class="hidden grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/business-people-shaking-hands-together_53876-30588.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Job Assistance</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/immigration-concept-with-passport_23-2149707876.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">UK Visa Guidance</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/doctor-with-his-arms-crossed-white-background_1368-5790.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Healthcare Registration</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/lawyer-explaining-clients-their-rights_74855-5261.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Legal Support</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-16 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12 text-black">Our Trusted Partners</h2>

            <div class="flex flex-wrap justify-center items-center gap-8 md:gap-16">
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/a/aa/HSBC_logo_%282018%29.svg/2560px-HSBC_logo_%282018%29.svg.png"
                        alt="HSBC" class="partner-logo-img">
                    <span class="partner-name">HSBC</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/de/Barclays_Bank_logo.svg/1200px-Barclays_Bank_logo.svg.png"
                        alt="Barclays" class="partner-logo-img">
                    <span class="partner-name">Barclays</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/Rightmove_logo.svg/2560px-Rightmove_logo.svg.png"
                        alt="Rightmove" class="partner-logo-img">
                    <span class="partner-name">Rightmove</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/4/4a/Gov.uk_logo.svg/1200px-Gov.uk_logo.svg.png"
                        alt="GOV.UK" class="partner-logo-img">
                    <span class="partner-name">GOV.UK</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/7c/British_Council_logo.svg/2560px-British_Council_logo.svg.png"
                        alt="British Council" class="partner-logo-img">
                    <span class="partner-name">British Council</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/03/NHS_England_logo.svg/1200px-NHS_England_logo.svg.png"
                        alt="NHS" class="partner-logo-img">
                    <span class="partner-name">NHS</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/58/Uber_logo_2018.svg/2560px-Uber_logo_2018.svg.png"
                        alt="Uber" class="partner-logo-img">
                    <span class="partner-name">Uber</span>
                </div>
                <div class="partner-logo-container">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/8/8e/Vodafone_2017_logo.svg/2560px-Vodafone_2017_logo.svg.png"
                        alt="Vodafone" class="partner-logo-img">
                    <span class="partner-name">Vodafone</span>
                </div>
            </div>
        </div>
    </section>

    <section id="cities" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="flex justify-between items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-black">Explore UK Cities</h2>
                <button id="explore-more-cities" class="text-black font-medium flex items-center">
                    View All <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="London" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">London</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££££ Expensive</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">The vibrant capital with endless opportunities and diverse
                            communities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,800</span>
                            <span><i class="fas fa-users mr-1"></i> 8.9M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                        alt="Manchester" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Manchester</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">A thriving northern city with great culture and affordable living.
                        </p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £950</span>
                            <span><i class="fas fa-users mr-1"></i> 2.8M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1508050919630-b135583b29ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Edinburgh" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Edinburgh</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's beautiful capital with historic charm and great
                            universities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,100</span>
                            <span><i class="fas fa-users mr-1"></i> 548,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1601823984263-b87b59798a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Birmingham" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Birmingham</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">England's second city with a thriving business scene and cultural
                            diversity.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £850</span>
                            <span><i class="fas fa-users mr-1"></i> 1.1M people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1571494146906-86de15d3817b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="Glasgow" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Glasgow</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's largest city with vibrant arts scene and friendly
                            locals.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £750</span>
                            <span><i class="fas fa-users mr-1"></i> 635,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="hidden city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1584696049838-8e2d9a3bfa8a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="Liverpool" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Liverpool</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Famous for its music heritage, waterfront and friendly locals.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £700</span>
                            <span><i class="fas fa-users mr-1"></i> 498,000 people</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">UK Survival Toolkit</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Essential knowledge and resources to help you settle in the UK quickly and comfortably.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-university"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Open a Bank Account</h3>
                    <p class="text-gray-600 text-sm">
                        Step-by-step guide to opening your first UK bank account as a newcomer.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Get NHS Number</h3>
                    <p class="text-gray-600 text-sm">
                        How to register with a GP and access UK healthcare services.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Essential Documents</h3>
                    <p class="text-gray-600 text-sm">
                        Checklist of documents needed for housing, schools, and employment.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-train"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Public Transport</h3>
                    <p class="text-gray-600 text-sm">
                        Navigating buses, trains, and the London Underground system.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="btn-black px-10 py-3 rounded-full font-medium text-lg relative overflow-hidden">
                    <span>Download Full Toolkit (PDF)</span>
                </button>
            </div>
        </div>
    </section>

    <section class="neighborhood-section">
        <div class="neighborhood-container">
            <div class="flex flex-col md:flex-row items-center gap-12">
                <div class="md:w-1/2">
                    <h2 class="text-4xl md:text-5xl font-bold mb-6 text-white">Find Your Perfect Neighborhood in the UK
                    </h2>
                    <p class="text-xl md:text-2xl mb-8 text-white">
                        Discover the best areas to live based on your lifestyle, budget, and family needs. Our
                        comprehensive guides help you make informed decisions about where to settle.
                    </p>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="bg-white p-3 rounded-full mr-4" style="color: #4441da;">
                                <i class="fas fa-map-marker-alt text-xl"></i>
                            </div>
                            <p class="text-white text-lg">Personalized area recommendations</p>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-white p-3 rounded-full mr-4" style="color: #4441da;">
                                <i class="fas fa-chart-line text-xl"></i>
                            </div>
                            <p class="text-white text-lg">Detailed cost of living comparisons</p>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-white p-3 rounded-full mr-4" style="color: #4441da;">
                                <i class="fas fa-users text-xl"></i>
                            </div>
                            <p class="text-white text-lg">Community and expat insights</p>
                        </div>
                    </div>
                    <button
                        class="bg-white text-black px-12 py-4 rounded-full font-medium text-lg hover:bg-gray-100 shadow-lg mt-8">
                        Explore Neighborhoods <i class="fas fa-search ml-2"></i>
                    </button>
                </div>
                <div class="md:w-1/2">
                    <img src="https://img.freepik.com/premium-photo/london-tower-bridge-thames-river_79295-6823.jpg?w=1380"
                        alt="London Neighborhood" class="neighborhood-image w-full h-auto">
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">What Our Customers Say</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Hear from families who have successfully relocated to the UK with our help.
            </p>

            <div class="testimonial-grid mb-12">
                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="Sophie Martin"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Sophie Martin</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/fr.png" alt="France" class="w-5 mr-2">
                                <span class="text-gray-600">From France • March 2023</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "MUVEAZY made our move to London seamless. Their housing specialist found us the perfect
                        apartment in just two weeks. The schooling advice was invaluable for our children's transition."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Raj Patel"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Raj Patel</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 mr-2">
                                <span class="text-gray-600">From India • January 2023</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "As a single professional moving to Manchester, I didn't know where to start. MUVEAZY's
                        relocation plan covered everything from visa support to finding the right neighborhood for young
                        professionals."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Elena Rodriguez"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Elena Rodriguez</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/es.png" alt="Spain" class="w-5 mr-2">
                                <span class="text-gray-600">From Spain • November 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The community adviser connected us with other Spanish families in Edinburgh before we even
                        arrived. Having that support network made all the difference in settling in."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                    </div>
                </div>
            </div>

            <div class="testimonial-grid">
                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Michael Johnson"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Michael Johnson</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/us.png" alt="USA" class="w-5 mr-2">
                                <span class="text-gray-600">From USA • September 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The legal support team helped navigate my work visa requirements with ease. They anticipated
                        every question I had before I even asked."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Aisha Mohammed"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Aisha Mohammed</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/ae.png" alt="UAE" class="w-5 mr-2">
                                <span class="text-gray-600">From UAE • July 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "Finding halal food options and mosques in Birmingham was my biggest concern. MUVEAZY's cultural
                        integration guide was exactly what I needed."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="far fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/55.jpg" alt="Wei Zhang"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Wei Zhang</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/cn.png" alt="China" class="w-5 mr-2">
                                <span class="text-gray-600">From China • May 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The Mandarin-speaking adviser helped my elderly parents feel comfortable with the healthcare
                        system. This personal touch meant everything."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-black text-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Begin Your UK Journey?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Get personalized relocation support tailored to your specific needs and circumstances.
            </p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <button class="bg-white text-black px-12 py-4 rounded-full font-medium hover:bg-gray-100 shadow-lg">
                    <span>Get Started</span>
                </button>
                <button
                    class="border-2 border-white text-white px-12 py-4 rounded-full font-medium hover:bg-white hover:text-black text-lg transition duration-300">
                    Speak to an Adviser
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-white py-12 px-6 border-t border-gray-200">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="text-xl font-bold tracking-tight"><span class="logo-m">M</span>UVEAZY</span>
                    </div>
                    <p class="text-gray-600 mb-4">
                        Helping individuals and families relocate to the UK with confidence since 2015.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#services" class="text-gray-600 hover:text-black">Housing Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Schooling Guidance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Visa Support</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Job Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Legal Support</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#cities" class="text-gray-600 hover:text-black">UK City Guides</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Cost of Living</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Relocation Checklist</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">UK Culture Guide</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Expat Community</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Newsletter</h3>
                    <p class="text-gray-600 mb-4">
                        Subscribe for relocation tips and UK updates.
                    </p>
                    <form class="flex">
                        <input type="email" placeholder="Your email"
                            class="px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-black w-full">
                        <button type="submit" class="bg-black text-white px-4 py-3 rounded-r-lg">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-600 mb-4 md:mb-0">
                    © 2023 MUVEAZY. All rights reserved.
                </p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-600 hover:text-black">Privacy Policy</a>
                    <a href="#" class="text-gray-600 hover:text-black">Terms of Service</a>
                    <a href="#" class="text-gray-600 hover:text-black">Contact Us</a>
                </div>
            </div>

            <div class="text-center mt-8">
                <p class="text-gray-600">
                    Made with <i class="fas fa-heart" style="color: #4441da;"></i> for UK Movers
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Sticky nav active link highlighting
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');

        window.addEventListener('scroll', () => {
            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;

                if (window.pageYOffset >= (sectionTop - 300)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').includes(current)) {
                    link.classList.add('active');
                }
            });
        });

        // View All Services functionality
        const viewAllBtn = document.getElementById('view-all-services');
        const hiddenServices = document.getElementById('hidden-services');

        viewAllBtn.addEventListener('click', () => {
            hiddenServices.classList.toggle('hidden');
            viewAllBtn.innerHTML = hiddenServices.classList.contains('hidden') ?
                'View All <i class="fas fa-arrow-right ml-2"></i>' :
                'Show Less <i class="fas fa-arrow-up ml-2"></i>';
        });

        // Explore More Cities functionality
        const exploreMoreBtn = document.getElementById('explore-more-cities');
        const hiddenCities = document.querySelectorAll('.hidden.city-card');

        exploreMoreBtn.addEventListener('click', () => {
            hiddenCities.forEach(city => {
                city.classList.toggle('hidden');
            });
            exploreMoreBtn.innerHTML = !hiddenCities[0].classList.contains('hidden') ?
                'Show Less <i class="fas fa-arrow-up ml-2"></i>' :
                'View All <i class="fas fa-arrow-right ml-2"></i>';
        });

        // Mobile menu toggle (would need implementation)
        const mobileMenuButton = document.querySelector('.md\\:hidden');
        mobileMenuButton.addEventListener('click', () => {
            // This would need a mobile menu implementation
            alert('Mobile menu would open here');
        });
    </script>
</body>

</html> -->


<!-- <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MUVEAZY - UK Relocation Experts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            color: #1a1a1a;
            background-color: #ffffff;
        }

        .hero-bg {
            position: relative;
            overflow: hidden;
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.6)),
                url('https://img.freepik.com/free-photo/big-ben-houses-parliament-london-uk_268835-1400.jpg?t=st=1747978770~exp=1747982370~hmac=49d3e4e1b87d77b54307bfcd1357288bbacfb73f9c0cee10ca817a4710136158&w=1380') no-repeat center center;
            background-size: cover;
            color: white;
        }

        .header-blur {
            backdrop-filter: blur(8px);
            background-color: rgba(255, 255, 255, 0.2);
        }

        .uk-flag {
            display: inline-block;
            width: 60px;
            height: 40px;
            background: linear-gradient(135deg, #012169 0%, #012169 33%, #C8102E 33%, #C8102E 66%, #FFFFFF 66%, #FFFFFF 100%);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            margin-left: 15px;
            vertical-align: middle;
            transform: translateY(-5px);
            animation: floatFlag 4s ease-in-out infinite;
        }

        @keyframes floatFlag {
            0% {
                transform: translateY(-5px) rotate(0deg);
            }

            50% {
                transform: translateY(-15px) rotate(5deg);
            }

            100% {
                transform: translateY(-5px) rotate(0deg);
            }
        }

        .floating-icon {
            position: absolute;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 10;
            animation: float 6s ease-in-out infinite;
        }

        .floating-icon:hover {
            transform: scale(1.1) translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .floating-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .floating-icon span {
            margin-top: 8px;
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
            text-align: center;
        }

        @keyframes float {
            0% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-15px);
            }

            100% {
                transform: translateY(0px);
            }
        }

        .service-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            height: 350px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        }

        .service-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.5));
            z-index: 1;
        }

        .service-card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .service-title-bg {
            backdrop-filter: blur(5px);
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-title-bg {
            background-color: rgba(68, 65, 218, 0.9);
        }

        .service-card:hover .service-title-bg h3 {
            color: white;
        }

        .neighborhood-section {
            position: relative;
            padding: 80px 0;
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)),
                url('https://img.freepik.com/free-photo/big-ben-houses-parliament-london-uk_268835-1400.jpg?t=st=1747978770~exp=1747982370~hmac=49d3e4e1b87d77b54307bfcd1357288bbacfb73f9c0cee10ca817a4710136158&w=1380');
            background-size: cover;
            background-position: center;
            color: white;
        }

        .neighborhood-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 100px;
        }

        .neighborhood-image {
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .neighborhood-image:hover {
            transform: scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        .testimonial-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .gradient-text {
            background: linear-gradient(94deg, #FFBF00 4.29%, #E151FF 20.14%, #9C83FF 61.14%, #714DFF 92.82%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .floating-icons-container {
            position: relative;
            width: 100%;
            height: 400px;
            background: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></svg>');
            background-size: 40px 40px;
            margin: 40px 0;
        }

        @media (max-width: 1024px) {
            .testimonial-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .neighborhood-container {
                padding: 0 50px;
            }
        }

        @media (max-width: 768px) {
            .testimonial-grid {
                grid-template-columns: 1fr;
            }

            .neighborhood-container {
                padding: 0 30px;
            }

            .hero-heading {
                font-size: 2.5rem;
            }

            .service-card {
                height: 280px;
            }

            .floating-icons-container {
                height: 600px;
            }

            .floating-icon {
                width: 60px;
                height: 60px;
            }
        }

        @media (max-width: 640px) {
            .neighborhood-container {
                padding: 0 20px;
            }
        }

        .btn-primary {
            background: linear-gradient(94deg, #FFBF00 4.29%, #E151FF 20.14%, #9C83FF 61.14%, #714DFF 92.82%);
            color: white;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            opacity: 0.9;
        }

        .btn-white {
            background: white;
            color: black;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-white:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .btn-black {
            background: #000000;
            color: white;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .btn-black:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            background: #333333;
        }

        .logo-m {
            color: #714DFF;
            font-weight: 800;
            font-size: 24px;
        }

        .partner-badge {
            background: white;
            border-radius: 50px;
            padding: 10px 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .partner-badge:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>

<body>
    <nav class="sticky-nav py-4 px-6 fade-in header-blur shadow-sm">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <span class="text-xl font-bold tracking-tight"><span class="logo-m">M</span>UVEAZY</span>
            </div>
            <div class="hidden md:flex space-x-8">
                <a href="#services" class="nav-link text-white hover:text-gray-200">Services</a>
                <a href="#community" class="nav-link text-white hover:text-gray-200">Community Advice</a>
                <a href="#support" class="nav-link text-white hover:text-gray-200">Support</a>
                <a href="#jobs" class="nav-link text-white hover:text-gray-200">Jobs in UK</a>
                <a href="#legal" class="nav-link text-white hover:text-gray-200">Legal Help</a>
            </div>
            <div class="flex items-center space-x-4">
                <button class="hidden md:flex items-center px-6 py-2 btn-white rounded-full font-medium">
                    Login <i class="fas fa-arrow-right ml-2 text-sm"></i>
                </button>
                <button class="md:hidden">
                    <i class="fas fa-bars text-xl text-white"></i>
                </button>
            </div>
        </div>
    </nav>

    <section class="hero-bg min-h-screen flex items-center justify-center text-center px-4">
        <div class="hero-content container mx-auto max-w-5xl relative">
            <h1 class="hero-heading text-5xl md:text-6xl font-bold mb-6 leading-tight text-white">
                <span class="block fade-in">Make the <span class="uk-flag"></span> Your New Home</span>
                <span class="block fade-in-delay-1">With Confidence & Support</span>
            </h1>
            <p class="text-lg md:text-xl mb-10 max-w-3xl mx-auto text-gray-200 fade-in-delay-2">
                MUVEAZY is your one-stop UK relocation platform covering housing, education, finance, and local
                integration. We provide personalized guidance to make your transition smooth and successful.
            </p>
            <button
                class="btn-primary px-12 py-4 rounded-full font-medium text-lg relative overflow-hidden fade-in-delay-3">
                <span>Get Started <i class="fas fa-arrow-right ml-2"></i></span>
            </button>
        </div>

<div class="floating-icons-container">
    <div class="floating-icon" style="top: 15%; left: 15%; background-color: #FF5733;">
        <i class="fas fa-home"></i>
        <span>Housing</span>
    </div>
    <div class="floating-icon" style="top: 35%; left: 10%; background-color: #33FF57;">
        <i class="fas fa-graduation-cap"></i>
        <span>Education</span>
    </div>
    <div class="floating-icon" style="top: 55%; left: 15%; background-color: #3357FF;">
        <i class="fas fa-pound-sign"></i>
        <span>Finance</span>
    </div>

    <div class="floating-icon" style="top: 15%; right: 15%; background-color: #E151FF;">
        <i class="fas fa-users"></i>
        <span>Community</span>
    </div>
    <div class="floating-icon" style="top: 35%; right: 10%; background-color: #FFBF00;">
        <i class="fas fa-briefcase"></i>
        <span>Jobs</span>
    </div>
    <div class="floating-icon" style="top: 55%; right: 15%; background-color: #714DFF;">
        <i class="fas fa-balance-scale"></i>
        <span>Legal</span>
    </div>
</div>
</section>

<section id="services" class="py-20 px-6 bg-white">
    <div class="container mx-auto">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold mb-4 gradient-text">Our Core Services</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Comprehensive relocation services tailored to your needs for a smooth transition to the UK
            </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="service-card rounded-xl"
                style="background-image: url('https://img.freepik.com/free-photo/front-view-front-door-with-white-wall-plants_23-2149360608.jpg?t=st=1747978103~exp=1747981703~hmac=da2efafbfd8b4c29c6ece6c3ab503f39361299d29cc5399ba63a89ea6ee3ad23&w=740')">
                <div class="service-card-content h-full flex flex-col justify-end p-6">
                    <div class="service-title-bg px-4 py-3 rounded-lg">
                        <h3 class="text-xl font-bold">Housing</h3>
                        <p class="text-sm mt-1">Find your perfect home in the UK</p>
                    </div>
                </div>
            </div>

            <div class="service-card rounded-xl"
                style="background-image: url('https://img.freepik.com/free-photo/group-diverse-kids-studying-together_53876-95845.jpg')">
                <div class="service-card-content h-full flex flex-col justify-end p-6">
                    <div class="service-title-bg px-4 py-3 rounded-lg">
                        <h3 class="text-xl font-bold">Schools</h3>
                        <p class="text-sm mt-1">Quality education for your children</p>
                    </div>
                </div>
            </div>

            <div class="service-card rounded-xl"
                style="background-image: url('https://img.freepik.com/free-photo/business-people-working-finance-accounting-analyze-financi_74952-1379.jpg')">
                <div class="service-card-content h-full flex flex-col justify-end p-6">
                    <div class="service-title-bg px-4 py-3 rounded-lg">
                        <h3 class="text-xl font-bold">Banking</h3>
                        <p class="text-sm mt-1">Set up your UK financial accounts</p>
                    </div>
                </div>
            </div>

            <div class="service-card rounded-xl"
                style="background-image: url('https://img.freepik.com/free-photo/group-diverse-people-having-business-meeting_53876-95060.jpg')">
                <div class="service-card-content h-full flex flex-col justify-end p-6">
                    <div class="service-title-bg px-4 py-3 rounded-lg">
                        <h3 class="text-xl font-bold">Community</h3>
                        <p class="text-sm mt-1">Connect with local expat networks</p>
                    </div>
                </div>
            </div>
        </div>

        <div id="hidden-services" class="hidden grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
            <div class="service-card rounded-xl"
                style="background-image: url('https://img.freepik.com/free-photo/business-people-shaking-hands-together_53876-30588.jpg')">
                <div class="service-card-content h-full flex flex-col justify-end p-6">
                    <div class="service-title-bg px-4 py-3 rounded-lg">
                        <h3 class="text-xl font-bold">Job Assistance</h3>
                        <p class="text-sm mt-1">Find employment opportunities</p>
                    </div>
                </div>
            </div>

            <div class="service-card rounded-xl"
                style="background-image: url('https://img.freepik.com/free-photo/immigration-concept-with-passport_23-2149707876.jpg')">
                <div class="service-card-content h-full flex flex-col justify-end p-6">
                    <div class="service-title-bg px-4 py-3 rounded-lg">
                        <h3 class="text-xl font-bold">UK Visa Guidance</h3>
                        <p class="text-sm mt-1">Navigate the visa process</p>
                    </div>
                </div>
            </div>

            <div class="service-card rounded-xl"
                style="background-image: url('https://img.freepik.com/free-photo/doctor-with-his-arms-crossed-white-background_1368-5790.jpg')">
                <div class="service-card-content h-full flex flex-col justify-end p-6">
                    <div class="service-title-bg px-4 py-3 rounded-lg">
                        <h3 class="text-xl font-bold">Healthcare</h3>
                        <p class="text-sm mt-1">Register with NHS services</p>
                    </div>
                </div>
            </div>

            <div class="service-card rounded-xl"
                style="background-image: url('https://img.freepik.com/free-photo/lawyer-explaining-clients-their-rights_74855-5261.jpg')">
                <div class="service-card-content h-full flex flex-col justify-end p-6">
                    <div class="service-title-bg px-4 py-3 rounded-lg">
                        <h3 class="text-xl font-bold">Legal Support</h3>
                        <p class="text-sm mt-1">Expert legal advice</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-12">
            <button id="view-all-services" class="btn-primary px-10 py-3 rounded-full font-medium text-lg">
                <span>View All Services</span>
            </button>
        </div>
    </div>
</section>

<section class="py-16 px-6 bg-gray-50">
    <div class="container mx-auto">
        <h2 class="text-3xl font-bold text-center mb-12 text-black">Our Trusted Partners</h2>

        <div class="flex flex-wrap justify-center items-center gap-4 md:gap-8">
            <div class="partner-badge">HSBC</div>
            <div class="partner-badge">Barclays</div>
            <div class="partner-badge">Rightmove</div>
            <div class="partner-badge">GOV.UK</div>
            <div class="partner-badge">British Council</div>
            <div class="partner-badge">NHS</div>
            <div class="partner-badge">Uber</div>
            <div class="partner-badge">Vodafone</div>
            <div class="partner-badge">Lloyds</div>
            <div class="partner-badge">Santander</div>
        </div>
    </div>
</section>

<section id="cities" class="py-20 px-6 bg-white">
    <div class="container mx-auto">
        <div class="flex justify-between items-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-black">Explore UK Cities</h2>
            <button id="explore-more-cities" class="text-black font-medium flex items-center">
                View All <i class="fas fa-arrow-right ml-2"></i>
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                    alt="London" class="w-full h-48 object-cover">
                <div class="p-6">
                    <h3 class="text-xl font-bold mb-2">London</h3>
                    <div class="flex items-center mb-3">
                        <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££££ Expensive</span>
                        <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                            Friendly</span>
                    </div>
                    <p class="text-gray-600 mb-4">The vibrant capital with endless opportunities and diverse
                        communities.</p>
                    <div class="flex justify-between text-sm text-gray-500">
                        <span><i class="fas fa-home mr-1"></i> Avg rent: £1,800</span>
                        <span><i class="fas fa-users mr-1"></i> 8.9M people</span>
                    </div>
                </div>
            </div>

            <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                    alt="Manchester" class="w-full h-48 object-cover">
                <div class="p-6">
                    <h3 class="text-xl font-bold mb-2">Manchester</h3>
                    <div class="flex items-center mb-3">
                        <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                        <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                            Friendly</span>
                    </div>
                    <p class="text-gray-600 mb-4">A thriving northern city with great culture and affordable living.
                    </p>
                    <div class="flex justify-between text-sm text-gray-500">
                        <span><i class="fas fa-home mr-1"></i> Avg rent: £950</span>
                        <span><i class="fas fa-users mr-1"></i> 2.8M people</span>
                    </div>
                </div>
            </div>

            <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                <img src="https://images.unsplash.com/photo-1508050919630-b135583b29ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                    alt="Edinburgh" class="w-full h-48 object-cover">
                <div class="p-6">
                    <h3 class="text-xl font-bold mb-2">Edinburgh</h3>
                    <div class="flex items-center mb-3">
                        <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                        <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                            Friendly</span>
                    </div>
                    <p class="text-gray-600 mb-4">Scotland's beautiful capital with historic charm and great
                        universities.</p>
                    <div class="flex justify-between text-sm text-gray-500">
                        <span><i class="fas fa-home mr-1"></i> Avg rent: £1,100</span>
                        <span><i class="fas fa-users mr-1"></i> 548,000 people</span>
                    </div>
                </div>
            </div>

            <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                <img src="https://images.unsplash.com/photo-1601823984263-b87b59798a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                    alt="Birmingham" class="w-full h-48 object-cover">
                <div class="p-6">
                    <h3 class="text-xl font-bold mb-2">Birmingham</h3>
                    <div class="flex items-center mb-3">
                        <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                        <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                            Friendly</span>
                    </div>
                    <p class="text-gray-600 mb-4">England's second city with a thriving business scene and cultural
                        diversity.</p>
                    <div class="flex justify-between text-sm text-gray-500">
                        <span><i class="fas fa-home mr-1"></i> Avg rent: £850</span>
                        <span><i class="fas fa-users mr-1"></i> 1.1M people</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="py-20 px-6 bg-gray-50">
    <div class="container mx-auto">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">UK Survival Toolkit</h2>
        <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
            Essential knowledge and resources to help you settle in the UK quickly and comfortably.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                <div class="text-3xl mb-4" style="color: #4441da;">
                    <i class="fas fa-university"></i>
                </div>
                <h3 class="text-lg font-bold mb-2">Open a Bank Account</h3>
                <p class="text-gray-600 text-sm">
                    Step-by-step guide to opening your first UK bank account as a newcomer.
                </p>
            </div>

            <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                <div class="text-3xl mb-4" style="color: #4441da;">
                    <i class="fas fa-heartbeat"></i>
                </div>
                <h3 class="text-lg font-bold mb-2">Get NHS Number</h3>
                <p class="text-gray-600 text-sm">
                    How to register with a GP and access UK healthcare services.
                </p>
            </div>

            <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                <div class="text-3xl mb-4" style="color: #4441da;">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3 class="text-lg font-bold mb-2">Essential Documents</h3>
                <p class="text-gray-600 text-sm">
                    Checklist of documents needed for housing, schools, and employment.
                </p>
            </div>

            <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                <div class="text-3xl mb-4" style="color: #4441da;">
                    <i class="fas fa-train"></i>
                </div>
                <h3 class="text-lg font-bold mb-2">Public Transport</h3>
                <p class="text-gray-600 text-sm">
                    Navigating buses, trains, and the London Underground system.
                </p>
            </div>
        </div>

        <div class="text-center mt-12">
            <button class="btn-primary px-10 py-3 rounded-full font-medium text-lg relative overflow-hidden">
                <span>Download Full Toolkit (PDF)</span>
            </button>
        </div>
    </div>
</section>

<section class="neighborhood-section">
    <div class="neighborhood-container text-center">
        <h2 class="text-4xl md:text-5xl font-bold mb-6 text-white">Find Your Perfect Neighborhood in the UK</h2>
        <p class="text-xl md:text-2xl mb-8 text-white max-w-3xl mx-auto">
            Discover the best areas to live based on your lifestyle, budget, and family needs. Our comprehensive
            guides help you make informed decisions about where to settle.
        </p>
        <div class="flex flex-col items-center space-y-4 mb-12">
            <div class="flex items-center">
                <div class="bg-white p-3 rounded-full mr-4" style="color: #4441da;">
                    <i class="fas fa-map-marker-alt text-xl"></i>
                </div>
                <p class="text-white text-lg">Personalized area recommendations</p>
            </div>
            <div class="flex items-center">
                <div class="bg-white p-3 rounded-full mr-4" style="color: #4441da;">
                    <i class="fas fa-chart-line text-xl"></i>
                </div>
                <p class="text-white text-lg">Detailed cost of living comparisons</p>
            </div>
            <div class="flex items-center">
                <div class="bg-white p-3 rounded-full mr-4" style="color: #4441da;">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <p class="text-white text-lg">Community and expat insights</p>
            </div>
        </div>
        <button class="bg-white text-black px-12 py-4 rounded-full font-medium hover:bg-gray-100 shadow-lg">
            Explore Neighborhoods <i class="fas fa-search ml-2"></i>
        </button>
    </div>
</section>

<section class="py-20 px-6 bg-white">
    <div class="container mx-auto">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">What Our Customers Say</h2>
        <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
            Hear from families who have successfully relocated to the UK with our help.
        </p>

        <div class="testimonial-grid mb-12">
            <div class="testimonial-card">
                <div class="flex items-center mb-6">
                    <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="Sophie Martin"
                        class="w-16 h-16 rounded-full object-cover">
                    <div class="ml-4">
                        <h4 class="font-bold">Sophie Martin</h4>
                        <div class="flex items-center mt-1">
                            <img src="https://flagcdn.com/w20/fr.png" alt="France" class="w-5 mr-2">
                            <span class="text-gray-600">From France • March 2023</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-700 italic">
                    "MUVEAZY made our move to London seamless. Their housing specialist found us the perfect
                    apartment in just two weeks. The schooling advice was invaluable for our children's transition."
                </p>
                <div class="mt-4">
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="flex items-center mb-6">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Raj Patel"
                        class="w-16 h-16 rounded-full object-cover">
                    <div class="ml-4">
                        <h4 class="font-bold">Raj Patel</h4>
                        <div class="flex items-center mt-1">
                            <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 mr-2">
                            <span class="text-gray-600">From India • January 2023</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-700 italic">
                    "As a single professional moving to Manchester, I didn't know where to start. MUVEAZY's
                    relocation plan covered everything from visa support to finding the right neighborhood for young
                    professionals."
                </p>
                <div class="mt-4">
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="flex items-center mb-6">
                    <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Elena Rodriguez"
                        class="w-16 h-16 rounded-full object-cover">
                    <div class="ml-4">
                        <h4 class="font-bold">Elena Rodriguez</h4>
                        <div class="flex items-center mt-1">
                            <img src="https://flagcdn.com/w20/es.png" alt="Spain" class="w-5 mr-2">
                            <span class="text-gray-600">From Spain • November 2022</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-700 italic">
                    "The community adviser connected us with other Spanish families in Edinburgh before we even
                    arrived. Having that support network made all the difference in settling in."
                </p>
                <div class="mt-4">
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star-half-alt text-yellow-400"></i>
                </div>
            </div>
        </div>

        <div class="testimonial-grid">
            <div class="testimonial-card">
                <div class="flex items-center mb-6">
                    <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Michael Johnson"
                        class="w-16 h-16 rounded-full object-cover">
                    <div class="ml-4">
                        <h4 class="font-bold">Michael Johnson</h4>
                        <div class="flex items-center mt-1">
                            <img src="https://flagcdn.com/w20/us.png" alt="USA" class="w-5 mr-2">
                            <span class="text-gray-600">From USA • September 2022</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-700 italic">
                    "The legal support team helped navigate my work visa requirements with ease. They anticipated
                    every question I had before I even asked."
                </p>
                <div class="mt-4">
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="flex items-center mb-6">
                    <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Aisha Mohammed"
                        class="w-16 h-16 rounded-full object-cover">
                    <div class="ml-4">
                        <h4 class="font-bold">Aisha Mohammed</h4>
                        <div class="flex items-center mt-1">
                            <img src="https://flagcdn.com/w20/ae.png" alt="UAE" class="w-5 mr-2">
                            <span class="text-gray-600">From UAE • July 2022</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-700 italic">
                    "Finding halal food options and mosques in Birmingham was my biggest concern. MUVEAZY's cultural
                    integration guide was exactly what I needed."
                </p>
                <div class="mt-4">
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="far fa-star text-yellow-400"></i>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="flex items-center mb-6">
                    <img src="https://randomuser.me/api/portraits/men/55.jpg" alt="Wei Zhang"
                        class="w-16 h-16 rounded-full object-cover">
                    <div class="ml-4">
                        <h4 class="font-bold">Wei Zhang</h4>
                        <div class="flex items-center mt-1">
                            <img src="https://flagcdn.com/w20/cn.png" alt="China" class="w-5 mr-2">
                            <span class="text-gray-600">From China • May 2022</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-700 italic">
                    "The Mandarin-speaking adviser helped my elderly parents feel comfortable with the healthcare
                    system. This personal touch meant everything."
                </p>
                <div class="mt-4">
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                    <i class="fas fa-star text-yellow-400"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="py-20 px-6 bg-black text-white">
    <div class="container mx-auto text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Begin Your UK Journey?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto">
            Get personalized relocation support tailored to your specific needs and circumstances.
        </p>
        <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <button class="bg-white text-black px-12 py-4 rounded-full font-medium hover:bg-gray-100 shadow-lg">
                <span>Get Started</span>
            </button>
            <button
                class="border-2 border-white text-white px-12 py-4 rounded-full font-medium hover:bg-white hover:text-black text-lg transition duration-300">
                Speak to an Adviser
            </button>
        </div>
    </div>
</section>

<footer class="bg-white py-12 px-6 border-t border-gray-200">
    <div class="container mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-xl font-bold tracking-tight"><span class="logo-m">M</span>UVEAZY</span>
                </div>
                <p class="text-gray-600 mb-4">
                    Helping individuals and families relocate to the UK with confidence since 2015.
                </p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-instagram"></i></a>
                </div>
            </div>

            <div>
                <h3 class="font-bold text-lg mb-4">Services</h3>
                <ul class="space-y-2">
                    <li><a href="#services" class="text-gray-600 hover:text-black">Housing Assistance</a></li>
                    <li><a href="#services" class="text-gray-600 hover:text-black">Schooling Guidance</a></li>
                    <li><a href="#services" class="text-gray-600 hover:text-black">Visa Support</a></li>
                    <li><a href="#services" class="text-gray-600 hover:text-black">Job Assistance</a></li>
                    <li><a href="#services" class="text-gray-600 hover:text-black">Legal Support</a></li>
                </ul>
            </div>

            <div>
                <h3 class="font-bold text-lg mb-4">Resources</h3>
                <ul class="space-y-2">
                    <li><a href="#cities" class="text-gray-600 hover:text-black">UK City Guides</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-black">Cost of Living</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-black">Relocation Checklist</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-black">UK Culture Guide</a></li>
                    <li><a href="#" class="text-gray-600 hover:text-black">Expat Community</a></li>
                </ul>
            </div>

            <div>
                <h3 class="font-bold text-lg mb-4">Newsletter</h3>
                <p class="text-gray-600 mb-4">
                    Subscribe for relocation tips and UK updates.
                </p>
                <form class="flex">
                    <input type="email" placeholder="Your email"
                        class="px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-black w-full">
                    <button type="submit" class="bg-black text-white px-4 py-3 rounded-r-lg">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>

        <div class="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-600 mb-4 md:mb-0">
                © 2023 MUVEAZY. All rights reserved.
            </p>
            <div class="flex space-x-6">
                <a href="#" class="text-gray-600 hover:text-black">Privacy Policy</a>
                <a href="#" class="text-gray-600 hover:text-black">Terms of Service</a>
                <a href="#" class="text-gray-600 hover:text-black">Contact Us</a>
            </div>
        </div>

        <div class="text-center mt-8">
            <p class="text-gray-600">
                Made with <i class="fas fa-heart" style="color: #4441da;"></i> for UK Movers
            </p>
        </div>
    </div>
</footer>

<script>
    // Sticky nav active link highlighting
    const sections = document.querySelectorAll('section');
    const navLinks = document.querySelectorAll('.nav-link');

    window.addEventListener('scroll', () => {
        let current = '';

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;

            if (window.pageYOffset >= (sectionTop - 300)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href').includes(current)) {
                link.classList.add('active');
            }
        });
    });

    // View All Services functionality
    const viewAllBtn = document.getElementById('view-all-services');
    const hiddenServices = document.getElementById('hidden-services');

    viewAllBtn.addEventListener('click', () => {
        hiddenServices.classList.toggle('hidden');
        viewAllBtn.innerHTML = hiddenServices.classList.contains('hidden') ?
            'View All Services' :
            'Show Less Services';
    });

    // Explore More Cities functionality
    const exploreMoreBtn = document.getElementById('explore-more-cities');
    const hiddenCities = document.querySelectorAll('.hidden.city-card');

    exploreMoreBtn.addEventListener('click', () => {
        hiddenCities.forEach(city => {
            city.classList.toggle('hidden');
        });
        exploreMoreBtn.innerHTML = !hiddenCities[0].classList.contains('hidden') ?
            'Show Less Cities <i class="fas fa-arrow-up ml-2"></i>' :
            'View All Cities <i class="fas fa-arrow-right ml-2"></i>';
    });

    // Mobile menu toggle (would need implementation)
    const mobileMenuButton = document.querySelector('.md\\:hidden');
    mobileMenuButton.addEventListener('click', () => {
        // This would need a mobile menu implementation
        alert('Mobile menu would open here');
    });
</script>
</body>

</html> -->


<!-- 

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MUVEAZY - UK Relocation Experts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            color: #1a1a1a;
            background-color: #ffffff;
        }

        .hero-bg {
            position: relative;
            overflow: hidden;
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.6)),
                url('https://img.freepik.com/free-photo/big-ben-houses-parliament-london-uk_268835-1400.jpg?t=st=1747978770~exp=1747982370~hmac=49d3e4e1b87d77b54307bfcd1357288bbacfb73f9c0cee10ca817a4710136158&w=1380') no-repeat center center;
            background-size: cover;
            color: white;
        }

        .sticky-nav {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.8);
        }

        .uk-flag {
            display: inline-block;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #012169 0%, #012169 33%, #C8102E 33%, #C8102E 66%, #FFFFFF 66%, #FFFFFF 100%);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            margin-left: 15px;
            vertical-align: middle;
            transform: translateY(-5px);
            animation: floatFlag 4s ease-in-out infinite;
        }

        @keyframes floatFlag {
            0% {
                transform: translateY(-5px) rotate(0deg);
            }

            50% {
                transform: translateY(-15px) rotate(5deg);
            }

            100% {
                transform: translateY(-5px) rotate(0deg);
            }
        }

        .floating-icon {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0px) rotateY(0deg);
            }

            50% {
                transform: translateY(-15px) rotateY(15deg);
            }

            100% {
                transform: translateY(0px) rotateY(0deg);
            }
        }

        .service-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            height: 350px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        }

        .service-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.5));
            z-index: 1;
        }

        .service-card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .service-title-bg {
            backdrop-filter: blur(5px);
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-title-bg {
            background-color: #4441da;
        }

        .service-card:hover .service-title-bg h3 {
            color: white;
        }

        .neighborhood-section {
            position: relative;
            padding: 80px 0;
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)),
                url('https://img.freepik.com/premium-photo/london-tower-bridge-thames-river_79295-6823.jpg?w=1380');
            background-size: cover;
            background-position: center;
            color: white;
        }

        .neighborhood-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 100px;
        }

        .neighborhood-image {
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .neighborhood-image:hover {
            transform: scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        .testimonial-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        @media (max-width: 1024px) {
            .testimonial-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .neighborhood-container {
                padding: 0 50px;
            }
        }

        @media (max-width: 768px) {
            .testimonial-grid {
                grid-template-columns: 1fr;
            }

            .neighborhood-container {
                padding: 0 30px;
            }

            .hero-heading {
                font-size: 2.5rem;
            }

            .service-card {
                height: 280px;
            }
        }

        @media (max-width: 640px) {
            .neighborhood-container {
                padding: 0 20px;
            }
        }

        .btn-theme {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .btn-theme:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        .logo-m {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: linear-gradient(94deg, #FFBF00 4.29%, #E151FF 20.14%, #9C83FF 61.14%, #714DFF 92.82%);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            font-weight: 800;
            margin-right: 8px;
            animation: rotateLogo 8s linear infinite;
        }

        @keyframes rotateLogo {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .partner-badge {
            background: white;
            border-radius: 50px;
            padding: 8px 20px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .partner-badge:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
        }

        .city-card {
            height: 420px;
            transition: all 0.3s ease;
        }

        .city-card:hover {
            transform: translateY(-10px);
        }
    </style>
</head>

<body>
    <nav class="sticky-nav py-4 px-6 fade-in shadow-sm fixed w-full z-50">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <span class="logo-m">M</span>
                <span class="text-xl font-bold tracking-tight">UVEAZY</span>
            </div>
            <div class="hidden md:flex space-x-8">
                <a href="#services" class="nav-link text-black hover:text-gray-600">Services</a>
                <a href="#community" class="nav-link text-black hover:text-gray-600">Community Advice</a>
                <a href="#support" class="nav-link text-black hover:text-gray-600">Support</a>
                <a href="#jobs" class="nav-link text-black hover:text-gray-600">Jobs in UK</a>
                <a href="#legal" class="nav-link text-black hover:text-gray-600">Legal Help</a>
            </div>
            <div class="flex items-center space-x-4">
                <button class="hidden md:flex items-center px-6 py-2 btn-theme rounded-full font-medium bg-black text-white">
                    Login <i class="fas fa-arrow-right ml-2 text-sm"></i>
                </button>
                <button class="md:hidden">
                    <i class="fas fa-bars text-xl text-black"></i>
                </button>
            </div>
        </div>
    </nav>

    <section class="hero-bg min-h-screen flex items-center justify-center text-center px-4 pt-20">
        <div class="hero-content container mx-auto max-w-5xl">
            <h1 class="hero-heading text-5xl md:text-6xl font-bold mb-6 leading-tight text-white">
                <span class="block fade-in">Make the UK Your New Home</span>
                <span class="block fade-in-delay-1">With Confidence & Support</span>
                <span class="uk-flag"></span>
            </h1>
            <p class="text-lg md:text-xl mb-10 max-w-3xl mx-auto text-gray-200 fade-in-delay-2">
                MUVEAZY is your one-stop UK relocation platform covering housing, education, finance, and local
                integration. We provide personalized guidance to make your transition smooth and successful.
            </p>
            <button
                class="btn-theme px-12 py-4 rounded-full font-medium text-lg bg-gradient-to-r from-yellow-400 to-purple-500 text-white">
                <span>Get Started <i class="fas fa-arrow-right ml-2"></i></span>
            </button>

            <div class="mt-20 flex flex-wrap justify-center gap-6 md:gap-10">
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #FF5733;">
                        <i class="fas fa-home text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Housing</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #33FF57;">
                        <i class="fas fa-graduation-cap text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Education</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #3357FF;">
                        <i class="fas fa-pound-sign text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Finance</span>
                </div>

                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #E151FF;">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Community</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #FFBF00;">
                        <i class="fas fa-briefcase text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Jobs</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #714DFF;">
                        <i class="fas fa-balance-scale text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Legal</span>
                </div>
            </div>
        </div>
    </section>

    <section id="services" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-purple-500 to-blue-500 bg-clip-text text-transparent">Our Core Services</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Comprehensive relocation services tailored to your needs</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/front-view-front-door-with-white-wall-plants_23-2149360608.jpg?t=st=1747978103~exp=1747981703~hmac=da2efafbfd8b4c29c6ece6c3ab503f39361299d29cc5399ba63a89ea6ee3ad23&w=740')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Housing</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/group-diverse-kids-studying-together_53876-95845.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Schools</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/business-people-working-finance-accounting-analyze-financi_74952-1379.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Banking</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/group-diverse-people-having-business-meeting_53876-95060.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Community Advice</h3>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="btn-theme px-10 py-3 rounded-full font-medium text-lg bg-black text-white">
                    View All Services <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <section class="py-16 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12 text-black">Our Trusted Partners</h2>

            <div class="flex flex-wrap justify-center items-center gap-4 md:gap-6">
                <div class="partner-badge">
                    <span class="font-medium">HSBC</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Barclays</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Rightmove</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">GOV.UK</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">British Council</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">NHS</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Uber</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Vodafone</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Lloyds</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Santander</span>
                </div>
            </div>
        </div>
    </section>

    <section id="cities" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-purple-500 to-blue-500 bg-clip-text text-transparent">Explore UK Cities</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Discover the perfect city for your new life in the UK</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="London" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">London</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££££ Expensive</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">The vibrant capital with endless opportunities and diverse
                            communities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,800</span>
                            <span><i class="fas fa-users mr-1"></i> 8.9M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                        alt="Manchester" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Manchester</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">A thriving northern city with great culture and affordable living.
                        </p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £950</span>
                            <span><i class="fas fa-users mr-1"></i> 2.8M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1508050919630-b135583b29ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Edinburgh" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Edinburgh</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's beautiful capital with historic charm and great
                            universities.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,100</span>
                            <span><i class="fas fa-users mr-1"></i> 548,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1601823984263-b87b59798a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Birmingham" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">Birmingham</h3>
                        <div class="flex items-center mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded ml-2">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">England's second city with a thriving business scene and cultural
                            diversity.</p>
                        <div class="flex justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £850</span>
                            <span><i class="fas fa-users mr-1"></i> 1.1M people</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="btn-theme px-10 py-3 rounded-full font-medium text-lg bg-gradient-to-r from-yellow-400 to-purple-500 text-white">
                    Explore More Cities <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">UK Survival Toolkit</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Essential knowledge and resources to help you settle in the UK quickly and comfortably.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-university"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Open a Bank Account</h3>
                    <p class="text-gray-600 text-sm">
                        Step-by-step guide to opening your first UK bank account as a newcomer.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Get NHS Number</h3>
                    <p class="text-gray-600 text-sm">
                        How to register with a GP and access UK healthcare services.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Essential Documents</h3>
                    <p class="text-gray-600 text-sm">
                        Checklist of documents needed for housing, schools, and employment.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-train"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Public Transport</h3>
                    <p class="text-gray-600 text-sm">
                        Navigating buses, trains, and the London Underground system.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="btn-theme px-10 py-3 rounded-full font-medium text-lg bg-black text-white">
                    <span>Download Full Toolkit (PDF)</span>
                </button>
            </div>
        </div>
    </section>

    <section class="neighborhood-section">
        <div class="neighborhood-container text-center">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 text-white">Find Your Perfect Neighborhood in the UK</h2>
                <p class="text-xl md:text-2xl mb-8 text-white">
                    Discover the best areas to live based on your lifestyle, budget, and family needs. Our
                    comprehensive guides help you make informed decisions about where to settle.
                </p>
                <div class="flex justify-center">
                    <button
                        class="bg-white text-black px-12 py-4 rounded-full font-medium text-lg hover:bg-gray-100 shadow-lg">
                        Explore Neighborhoods <i class="fas fa-search ml-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">What Our Customers Say</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Hear from families who have successfully relocated to the UK with our help.
            </p>

            <div class="testimonial-grid mb-12">
                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="Sophie Martin"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Sophie Martin</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/fr.png" alt="France" class="w-5 mr-2">
                                <span class="text-gray-600">From France • March 2023</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "MUVEAZY made our move to London seamless. Their housing specialist found us the perfect
                        apartment in just two weeks. The schooling advice was invaluable for our children's transition."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Raj Patel"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Raj Patel</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 mr-2">
                                <span class="text-gray-600">From India • January 2023</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "As a single professional moving to Manchester, I didn't know where to start. MUVEAZY's
                        relocation plan covered everything from visa support to finding the right neighborhood for young
                        professionals."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Elena Rodriguez"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Elena Rodriguez</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/es.png" alt="Spain" class="w-5 mr-2">
                                <span class="text-gray-600">From Spain • November 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The community adviser connected us with other Spanish families in Edinburgh before we even
                        arrived. Having that support network made all the difference in settling in."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                    </div>
                </div>
            </div>

            <div class="testimonial-grid">
                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Michael Johnson"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Michael Johnson</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/us.png" alt="USA" class="w-5 mr-2">
                                <span class="text-gray-600">From USA • September 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The legal support team helped navigate my work visa requirements with ease. They anticipated
                        every question I had before I even asked."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Aisha Mohammed"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Aisha Mohammed</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/ae.png" alt="UAE" class="w-5 mr-2">
                                <span class="text-gray-600">From UAE • July 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "Finding halal food options and mosques in Birmingham was my biggest concern. MUVEAZY's cultural
                        integration guide was exactly what I needed."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="far fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/55.jpg" alt="Wei Zhang"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Wei Zhang</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/cn.png" alt="China" class="w-5 mr-2">
                                <span class="text-gray-600">From China • May 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The Mandarin-speaking adviser helped my elderly parents feel comfortable with the healthcare
                        system. This personal touch meant everything."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-black text-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Begin Your UK Journey?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Get personalized relocation support tailored to your specific needs and circumstances.
            </p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <button class="bg-white text-black px-12 py-4 rounded-full font-medium hover:bg-gray-100 shadow-lg">
                    <span>Get Started</span>
                </button>
                <button
                    class="border-2 border-white text-white px-12 py-4 rounded-full font-medium hover:bg-white hover:text-black text-lg transition duration-300">
                    Speak to an Adviser
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-white py-12 px-6 border-t border-gray-200">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="logo-m">M</span>
                        <span class="text-xl font-bold tracking-tight">UVEAZY</span>
                    </div>
                    <p class="text-gray-600 mb-4">
                        Helping individuals and families relocate to the UK with confidence since 2015.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#services" class="text-gray-600 hover:text-black">Housing Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Schooling Guidance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Visa Support</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Job Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Legal Support</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#cities" class="text-gray-600 hover:text-black">UK City Guides</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Cost of Living</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Relocation Checklist</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">UK Culture Guide</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Expat Community</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Newsletter</h3>
                    <p class="text-gray-600 mb-4">
                        Subscribe for relocation tips and UK updates.
                    </p>
                    <form class="flex">
                        <input type="email" placeholder="Your email"
                            class="px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-black w-full">
                        <button type="submit" class="bg-black text-white px-4 py-3 rounded-r-lg">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-600 mb-4 md:mb-0">
                    © 2023 MUVEAZY. All rights reserved.
                </p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-600 hover:text-black">Privacy Policy</a>
                    <a href="#" class="text-gray-600 hover:text-black">Terms of Service</a>
                    <a href="#" class="text-gray-600 hover:text-black">Contact Us</a>
                </div>
            </div>

            <div class="text-center mt-8">
                <p class="text-gray-600">
                    Made with <i class="fas fa-heart" style="color: #4441da;"></i> for UK Movers
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Sticky nav active link highlighting
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');

        window.addEventListener('scroll', () => {
            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;

                if (window.pageYOffset >= (sectionTop - 300)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').includes(current)) {
                    link.classList.add('active');
                }
            });
        });

        // View All Services functionality
        const viewAllBtn = document.getElementById('view-all-services');
        const hiddenServices = document.getElementById('hidden-services');

        if (viewAllBtn && hiddenServices) {
            viewAllBtn.addEventListener('click', () => {
                hiddenServices.classList.toggle('hidden');
                viewAllBtn.innerHTML = hiddenServices.classList.contains('hidden') ?
                    'View All <i class="fas fa-arrow-right ml-2"></i>' :
                    'Show Less <i class="fas fa-arrow-up ml-2"></i>';
            });
        }

        // Explore More Cities functionality
        const exploreMoreBtn = document.getElementById('explore-more-cities');
        const hiddenCities = document.querySelectorAll('.hidden.city-card');

        if (exploreMoreBtn && hiddenCities.length > 0) {
            exploreMoreBtn.addEventListener('click', () => {
                hiddenCities.forEach(city => {
                    city.classList.toggle('hidden');
                });
                exploreMoreBtn.innerHTML = !hiddenCities[0].classList.contains('hidden') ?
                    'Show Less <i class="fas fa-arrow-up ml-2"></i>' :
                    'View All <i class="fas fa-arrow-right ml-2"></i>';
            });
        }

        // Mobile menu toggle (would need implementation)
        const mobileMenuButton = document.querySelector('.md\\:hidden');
        if (mobileMenuButton) {
            mobileMenuButton.addEventListener('click', () => {
                // This would need a mobile menu implementation
                alert('Mobile menu would open here');
            });
        }
    </script>
</body>

</html>

-->

<!-- Prompt -6 -->
<!-- <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MUVEAZY - UK Relocation Experts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            color: #1a1a1a;
            background-color: #ffffff;
        }

        .hero-bg {
            position: relative;
            overflow: hidden;
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.6)),
                url('https://img.freepik.com/free-photo/big-ben-houses-parliament-london-uk_268835-1400.jpg?t=st=1747978770~exp=1747982370~hmac=49d3e4e1b87d77b54307bfcd1357288bbacfb73f9c0cee10ca817a4710136158&w=1380') no-repeat center center;
            background-size: cover;
            color: white;
        }

        .sticky-nav {
            width: 98%;
            left: 1%;
            border-radius: 18px;
            background: #f9fafb;
            border: 2px solid white !important;
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        }

        .uk-flag {
            display: inline-block;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-image: url('https://cdn-icons-png.flaticon.com/512/197/197374.png');
            background-size: cover;
            margin-left: 10px;
            vertical-align: middle;
            transform: translateY(-2px);
            animation: floatFlag 4s ease-in-out infinite;
        }

        @keyframes floatFlag {
            0% {
                transform: translateY(-2px) rotate(0deg);
            }

            50% {
                transform: translateY(-8px) rotate(5deg);
            }

            100% {
                transform: translateY(-2px) rotate(0deg);
            }
        }

        .floating-icon {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0px) rotateY(0deg);
            }

            50% {
                transform: translateY(-15px) rotateY(15deg);
            }

            100% {
                transform: translateY(0px) rotateY(0deg);
            }
        }

        .service-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            height: 350px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        }

        .service-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.5));
            z-index: 1;
        }

        .service-card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .service-title-bg {
            backdrop-filter: blur(5px);
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-title-bg {
            background-color: #4441da;
        }

        .service-card:hover .service-title-bg h3 {
            color: white;
        }

        .neighborhood-section {
            position: relative;
            padding: 80px 0;
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)),
                url('https://img.freepik.com/premium-photo/london-tower-bridge-thames-river_79295-6823.jpg?w=1380');
            background-size: cover;
            background-position: center;
            color: white;
        }

        .neighborhood-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 100px;
        }

        .neighborhood-image {
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .neighborhood-image:hover {
            transform: scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        .testimonial-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        @media (max-width: 1024px) {
            .testimonial-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .neighborhood-container {
                padding: 0 50px;
            }
        }

        @media (max-width: 768px) {
            .testimonial-grid {
                grid-template-columns: 1fr;
            }

            .neighborhood-container {
                padding: 0 30px;
            }

            .hero-heading {
                font-size: 2.5rem;
            }

            .service-card {
                height: 280px;
            }
        }

        @media (max-width: 640px) {
            .neighborhood-container {
                padding: 0 20px;
            }
        }

        .btn-theme {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .btn-theme:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }

        .logo-m {
            display: inline-block;
            width: 40px;
            height: 40px;
            background-image: url('https://cdn-icons-png.flaticon.com/512/16895/16895102.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            margin-right: 8px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        .partner-badge {
            background: white;
            border-radius: 50px;
            padding: 8px 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .partner-badge:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }

        .city-card {
            height: 420px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .city-card:hover {
            transform: translateY(-10px);
        }

        .city-card img {
            height: 200px;
            object-fit: cover;
            width: 100%;
        }

        .city-card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 1.5rem;
        }

        /* Modern Navbar Styles */
        .nav-link {
            position: relative;
            padding: 8px 0;
            font-weight: 500;
            color: #4b5563;
            transition: all 0.3s ease;
        }

        .nav-link:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: #4f46e5;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: #111827;
        }

        .nav-link:hover:after {
            width: 100%;
        }

        .nav-link.active {
            color: #111827;
            font-weight: 600;
        }

        .nav-link.active:after {
            width: 100%;
        }

        .login-btn {
            background: linear-gradient(45deg, #4f46e5, #8b5cf6);
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
        }

        .mobile-menu-btn {
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            transform: scale(1.1);
        }
    </style>
</head>

<body>
    <nav class="sticky-nav py-4 px-6 fade-in fixed w-full z-50">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <span class="logo-m"></span>
                <span
                    class="text-xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">MUVEAZY</span>
            </div>
            <div class="hidden md:flex space-x-8 items-center">
                <a href="#services" class="nav-link">Services</a>
                <a href="#community" class="nav-link">Community Advice</a>
                <a href="#support" class="nav-link">Support</a>
                <a href="#jobs" class="nav-link">Jobs in UK</a>
                <a href="#legal" class="nav-link">Legal Help</a>
                <button class="login-btn px-6 py-2 rounded-full font-medium text-white shadow-md">
                    Login <i class="fas fa-arrow-right ml-2 text-sm"></i>
                </button>
            </div>
            <button class="md:hidden mobile-menu-btn">
                <i class="fas fa-bars text-xl text-gray-800"></i>
            </button>
        </div>
    </nav>

    <section class="hero-bg min-h-screen flex items-center justify-center text-center px-4 pt-20">
        <div class="hero-content container mx-auto max-w-5xl">
            <h1 class="hero-heading text-5xl md:text-6xl font-bold mb-6 leading-tight text-white">
                <span class="block fade-in">Make the <img src="https://cdn-icons-png.flaticon.com/512/197/197374.png"
                        alt="UK Flag" class="uk-flag "> Your New Home</span>
                <span class="block fade-in-delay-1">With Confidence & Support</span>

            </h1>
            <p class="text-lg md:text-xl mb-10 max-w-3xl mx-auto text-gray-200 fade-in-delay-2">
                MUVEAZY is your one-stop UK relocation platform covering housing, education, finance, and local
                integration. We provide personalized guidance to make your transition smooth and successful.
            </p>
            <button
                class="btn-theme px-12 py-4 rounded-full font-medium text-lg bg-gradient-to-r from-yellow-400 to-purple-500 text-white">
                <span>Get Started <i class="fas fa-arrow-right ml-2"></i></span>
            </button>

            <div class="mt-20 flex flex-wrap justify-center gap-6 md:gap-10">
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #FF5733;">
                        <i class="fas fa-home text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Housing</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #33FF57;">
                        <i class="fas fa-graduation-cap text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Education</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 px-7 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #3357FF;">
                        <i class="fas fa-pound-sign text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Finance</span>
                </div>

                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #E151FF;">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Community</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #FFBF00;">
                        <i class="fas fa-briefcase text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Jobs</span>
                </div>
                <div class="flex flex-col items-center service-icon-container floating-icon">
                    <div class="bg-white p-5 rounded-full shadow-lg mb-2 transform hover:rotate-6 transition duration-300"
                        style="color: #714DFF;">
                        <i class="fas fa-balance-scale text-2xl"></i>
                    </div>
                    <span class="text-sm font-medium text-white">Legal</span>
                </div>
            </div>
        </div>
    </section>

    <section id="services" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2
                    class="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-purple-500 to-blue-500 bg-clip-text text-transparent">
                    Our Core Services</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Comprehensive relocation services tailored to your needs</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/front-view-front-door-with-white-wall-plants_23-2149360608.jpg?t=st=1747978103~exp=1747981703~hmac=da2efafbfd8b4c29c6ece6c3ab503f39361299d29cc5399ba63a89ea6ee3ad23&w=740')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Housing</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/group-diverse-kids-studying-together_53876-95845.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Schools</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/business-people-working-finance-accounting-analyze-financi_74952-1379.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Banking</h3>
                        </div>
                    </div>
                </div>

                <div class="service-card rounded-xl"
                    style="background-image: url('https://img.freepik.com/free-photo/group-diverse-people-having-business-meeting_53876-95060.jpg')">
                    <div class="service-card-content h-full flex flex-col justify-end p-6">
                        <div class="service-title-bg px-4 py-3 rounded-lg">
                            <h3 class="text-xl font-bold">Community Advice</h3>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="btn-theme px-10 py-3 rounded-full font-medium text-lg bg-black text-white">
                    View All Services <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <section class="py-16 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12 text-black">Our Trusted Partners</h2>

            <div class="flex flex-wrap justify-center items-center gap-4 md:gap-6">
                <div class="partner-badge">
                    <span class="font-medium">HSBC</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Barclays</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Rightmove</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">GOV.UK</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">British Council</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">NHS</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Uber</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Vodafone</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Lloyds</span>
                </div>
                <div class="partner-badge">
                    <span class="font-medium">Santander</span>
                </div>
            </div>
        </div>
    </section>

    <section id="cities" class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2
                    class="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-purple-500 to-blue-500 bg-clip-text text-transparent">
                    Explore UK Cities</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Discover the perfect city for your new life in the UK</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                        alt="London" class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">London</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££££ Expensive</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">The vibrant capital with endless opportunities and diverse
                            communities.</p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,800</span>
                            <span><i class="fas fa-users mr-1"></i> 8.9M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1515586838455-8f8a940b7d38?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2093&q=80"
                        alt="Manchester" class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Manchester</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">A thriving northern city with great culture and affordable living.
                        </p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £950</span>
                            <span><i class="fas fa-users mr-1"></i> 2.8M people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1508050919630-b135583b29ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Edinburgh" class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Edinburgh</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">£££ Moderate</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">Scotland's beautiful capital with historic charm and great
                            universities.</p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £1,100</span>
                            <span><i class="fas fa-users mr-1"></i> 548,000 people</span>
                        </div>
                    </div>
                </div>

                <div class="city-card bg-white rounded-xl overflow-hidden shadow-md">
                    <img src="https://images.unsplash.com/photo-1601823984263-b87b59798a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                        alt="Birmingham" class="w-full h-64 object-cover">
                    <div class="city-card-content">
                        <h3 class="text-xl font-bold mb-2">Birmingham</h3>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <span class="text-sm font-medium bg-gray-100 px-2 py-1 rounded">££ Affordable</span>
                            <span class="text-sm font-medium bg-green-100 text-green-800 px-2 py-1 rounded">Expat
                                Friendly</span>
                        </div>
                        <p class="text-gray-600 mb-4">England's second city with a thriving business scene and cultural
                            diversity.</p>
                        <div class="mt-auto flex flex-wrap justify-between text-sm text-gray-500">
                            <span><i class="fas fa-home mr-1"></i> Avg rent: £850</span>
                            <span><i class="fas fa-users mr-1"></i> 1.1M people</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button
                    class="btn-theme px-10 py-3 rounded-full font-medium text-lg bg-gradient-to-r from-yellow-400 to-purple-500 text-white">
                    Explore More Cities <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-gray-50">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">UK Survival Toolkit</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Essential knowledge and resources to help you settle in the UK quickly and comfortably.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-university"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Open a Bank Account</h3>
                    <p class="text-gray-600 text-sm">
                        Step-by-step guide to opening your first UK bank account as a newcomer.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Get NHS Number</h3>
                    <p class="text-gray-600 text-sm">
                        How to register with a GP and access UK healthcare services.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Essential Documents</h3>
                    <p class="text-gray-600 text-sm">
                        Checklist of documents needed for housing, schools, and employment.
                    </p>
                </div>

                <div class="toolkit-item bg-white p-6 rounded-xl shadow-sm hover:shadow-md">
                    <div class="text-3xl mb-4" style="color: #4441da;">
                        <i class="fas fa-train"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Public Transport</h3>
                    <p class="text-gray-600 text-sm">
                        Navigating buses, trains, and the London Underground system.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="btn-theme px-10 py-3 rounded-full font-medium text-lg bg-black text-white">
                    <span>Download Full Toolkit (PDF)</span>
                </button>
            </div>
        </div>
    </section>

    <section class="neighborhood-section">
        <div class="neighborhood-container text-center">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 text-white">Find Your Perfect Neighborhood in the UK</h2>
                <p class="text-xl md:text-2xl mb-8 text-white">
                    Discover the best areas to live based on your lifestyle, budget, and family needs. Our
                    comprehensive guides help you make informed decisions about where to settle.
                </p>
                <div class="flex justify-center">
                    <button
                        class="bg-white text-black px-12 py-4 rounded-full font-medium text-lg hover:bg-gray-100 shadow-lg">
                        Explore Neighborhoods <i class="fas fa-search ml-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-white">
        <div class="container mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-4 text-black">What Our Customers Say</h2>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                Hear from families who have successfully relocated to the UK with our help.
            </p>

            <div class="testimonial-grid mb-12">
                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="Sophie Martin"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Sophie Martin</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/fr.png" alt="France" class="w-5 mr-2">
                                <span class="text-gray-600">From France • March 2023</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "MUVEAZY made our move to London seamless. Their housing specialist found us the perfect
                        apartment in just two weeks. The schooling advice was invaluable for our children's transition."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Raj Patel"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Raj Patel</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 mr-2">
                                <span class="text-gray-600">From India • January 2023</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "As a single professional moving to Manchester, I didn't know where to start. MUVEAZY's
                        relocation plan covered everything from visa support to finding the right neighborhood for young
                        professionals."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Elena Rodriguez"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Elena Rodriguez</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/es.png" alt="Spain" class="w-5 mr-2">
                                <span class="text-gray-600">From Spain • November 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The community adviser connected us with other Spanish families in Edinburgh before we even
                        arrived. Having that support network made all the difference in settling in."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                    </div>
                </div>
            </div>

            <div class="testimonial-grid">
                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Michael Johnson"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Michael Johnson</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/us.png" alt="USA" class="w-5 mr-2">
                                <span class="text-gray-600">From USA • September 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The legal support team helped navigate my work visa requirements with ease. They anticipated
                        every question I had before I even asked."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Aisha Mohammed"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Aisha Mohammed</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/ae.png" alt="UAE" class="w-5 mr-2">
                                <span class="text-gray-600">From UAE • July 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "Finding halal food options and mosques in Birmingham was my biggest concern. MUVEAZY's cultural
                        integration guide was exactly what I needed."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="far fa-star text-yellow-400"></i>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/55.jpg" alt="Wei Zhang"
                            class="w-16 h-16 rounded-full object-cover">
                        <div class="ml-4">
                            <h4 class="font-bold">Wei Zhang</h4>
                            <div class="flex items-center mt-1">
                                <img src="https://flagcdn.com/w20/cn.png" alt="China" class="w-5 mr-2">
                                <span class="text-gray-600">From China • May 2022</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-700 italic">
                        "The Mandarin-speaking adviser helped my elderly parents feel comfortable with the healthcare
                        system. This personal touch meant everything."
                    </p>
                    <div class="mt-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 px-6 bg-black text-white">
        <div class="container mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Begin Your UK Journey?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Get personalized relocation support tailored to your specific needs and circumstances.
            </p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <button class="bg-white text-black px-12 py-4 rounded-full font-medium hover:bg-gray-100 shadow-lg">
                    <span>Get Started</span>
                </button>
                <button
                    class="border-2 border-white text-white px-12 py-4 rounded-full font-medium hover:bg-white hover:text-black text-lg transition duration-300">
                    Speak to an Adviser
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-white py-12 px-6 border-t border-gray-200">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="logo-m"></span>
                        <span
                            class="text-xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">MUVEAZY</span>
                    </div>
                    <p class="text-gray-600 mb-4">
                        Helping individuals and families relocate to the UK with confidence since 2015.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-600 hover:text-black"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#services" class="text-gray-600 hover:text-black">Housing Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Schooling Guidance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Visa Support</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Job Assistance</a></li>
                        <li><a href="#services" class="text-gray-600 hover:text-black">Legal Support</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#cities" class="text-gray-600 hover:text-black">UK City Guides</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Cost of Living</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Relocation Checklist</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">UK Culture Guide</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-black">Expat Community</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-bold text-lg mb-4">Newsletter</h3>
                    <p class="text-gray-600 mb-4">
                        Subscribe for relocation tips and UK updates.
                    </p>
                    <form class="flex">
                        <input type="email" placeholder="Your email"
                            class="px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-black w-full">
                        <button type="submit" class="bg-black text-white px-4 py-3 rounded-r-lg">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-600 mb-4 md:mb-0">
                    © 2023 MUVEAZY. All rights reserved.
                </p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-600 hover:text-black">Privacy Policy</a>
                    <a href="#" class="text-gray-600 hover:text-black">Terms of Service</a>
                    <a href="#" class="text-gray-600 hover:text-black">Contact Us</a>
                </div>
            </div>

            <div class="text-center mt-8">
                <p class="text-gray-600">
                    Made with <i class="fas fa-heart" style="color: #4441da;"></i> for UK Movers
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Sticky nav active link highlighting
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');

        window.addEventListener('scroll', () => {
            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;

                if (window.pageYOffset >= (sectionTop - 300)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').includes(current)) {
                    link.classList.add('active');
                }
            });
        });

        // Mobile menu toggle (would need implementation)
        const mobileMenuButton = document.querySelector('.md\\:hidden');
        if (mobileMenuButton) {
            mobileMenuButton.addEventListener('click', () => {
                // This would need a mobile menu implementation
                alert('Mobile menu would open here');
            });
        }
    </script>
</body>

</html> -->

<!-- prompt-7 -->